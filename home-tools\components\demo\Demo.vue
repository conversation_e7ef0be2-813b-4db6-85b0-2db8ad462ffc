<template>
  <div>
    <div v-if="false">
      自动引入组件测试
    </div>
    <div v-html="htmlvalue"></div>
    <Editor class="h-[500px]" v-model="htmlvalue"/>
  </div>
</template>

<script setup>
const htmlvalue = ref('Dichotomization: <em>2</em> &times; <em>2</em> (&times;<em>2</em> &times; <em>2</em> &times; <em>2</em>...) categories: infinite possibilities')

</script>

<style lang="scss" scoped>

:deep(em) {
  color: red !important;
}

</style>