// generated by the @nuxtjs/tailwindcss <https://github.com/nuxt-modules/tailwindcss> module at 2025/8/4 16:40:18
import configMerger from "@nuxtjs/tailwindcss/merger";

import cfg2 from "./../../tailwind.config.js";
const config = [
{"content":{"files":["C:/Users/<USER>/medsci-develop/java/main/home-tools/components/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/medsci-develop/java/main/home-tools/components/global/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/medsci-develop/java/main/home-tools/components/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/medsci-develop/java/main/home-tools/layouts/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/medsci-develop/java/main/home-tools/plugins/**/*.{js,ts,mjs}","C:/Users/<USER>/medsci-develop/java/main/home-tools/composables/**/*.{js,ts,mjs}","C:/Users/<USER>/medsci-develop/java/main/home-tools/utils/**/*.{js,ts,mjs}","C:/Users/<USER>/medsci-develop/java/main/home-tools/pages/index.vue","C:/Users/<USER>/medsci-develop/java/main/home-tools/pages/sign-up.vue","C:/Users/<USER>/medsci-develop/java/main/home-tools/pages/login/index.vue","C:/Users/<USER>/medsci-develop/java/main/home-tools/pages/article/[id].vue","C:/Users/<USER>/medsci-develop/java/main/home-tools/pages/tool/destroy.vue","C:/Users/<USER>/medsci-develop/java/main/home-tools/pages/cases/[caseId].vue","C:/Users/<USER>/medsci-develop/java/main/home-tools/pages/chat/[appUuid].vue","C:/Users/<USER>/medsci-develop/java/main/home-tools/pages/tool/[appUuid].vue","C:/Users/<USER>/medsci-develop/java/main/home-tools/pages/write/[appUuid].vue","C:/Users/<USER>/medsci-develop/java/main/home-tools/pages/test-attachments.vue","C:/Users/<USER>/medsci-develop/java/main/home-tools/pages/payLink/[payInfo].vue","C:/Users/<USER>/medsci-develop/java/main/home-tools/pages/login/[socialType].vue","C:/Users/<USER>/medsci-develop/java/main/home-tools/pages/tool/privacy-policy.vue","C:/Users/<USER>/medsci-develop/java/main/home-tools/pages/tool/components/InputField.vue","C:/Users/<USER>/medsci-develop/java/main/home-tools/{A,a}pp.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/medsci-develop/java/main/home-tools/{E,e}rror.{vue,js,jsx,mjs,ts,tsx}","C:/Users/<USER>/medsci-develop/java/main/home-tools/app.config.{js,ts,mjs}"]}},
{},
cfg2
].reduce((acc, curr) => configMerger(acc, curr), {});

const resolvedConfig = config;

export default resolvedConfig;