/* light-theme.scss */
$primary-color: #f0f0f0;
$secondary-color: #2f5883ff;

.light-theme {
  display: flex;
  flex-direction: row;
  height: calc(var(--vh) * 100);
  background: url("@/assets/imgs/background2.png") no-repeat;
  background-size: 100% 100%;
  :deep(.github-markdown-body) h1,
  :deep(.github-markdown-body) h2,
  :deep(.github-markdown-body) h3,
  :deep(.github-markdown-body) h4 {
    font-size: 16px;
    border-bottom: 0;
    color: unset;
  }
  .left {
    width: 284px;
    height: 100%;
    position: relative;
    .empty-box {
      width: 100%;
      text-align: center;
      img {
        margin: 87px 0 4px 0;
        width: 68px;
        height: 68px;
      }
      p {
        color: #fff;
        font-size: 14px;
        font-weight: 500;
        font-family: "PingFang SC", sans-serif;
      }
    }
  }

  .right {
    width: calc(100% - 264px);
    opacity: 1;
    height: 100%;
    background-image: url("@/views/aiChat/components/images/right-bg.png");
  }

  header {
    position: relative;
    height: 50px;
    margin: -4px 3rem -13px;

    .openBtnLeft {
      cursor: pointer;
      position: absolute;
      top: 0px;
      left: 0px;
      display: flex;
      align-items: center;
      a{
        color: #666;
      }
      span{
        font-size: 14px;
      }
      img{
        width: 14px;
        margin-right: 5px;
      }
    }
    .openBtn {
      cursor: pointer;
      position: absolute;
      top: 0px;
      left: -40px;
      display: flex;
      align-items: center;
      a{
        color: #333;
      }
      span{
        font-size: 14px;
        color: #666;
      }
      img{
        width: 14px;
        margin-right: 5px;
      }
    }
  }
  .topTitle {
    display: flex;
    margin-top: 50px;
    justify-content: center;
    align-items: center;
    img {
      // width: 79px;
      height: 24px;
    }
    span{
      margin-left: 5px;
     a{
      font-size: 18px;
      line-height: 1;
     }
    }
  }

  .chatBar {
    // background: #12152a;
    // background: url("../components/images/navBar_pc.png");
    background-size: 100% 100%;
    height: calc(var(--vh) * 100);
    padding-top: 1px;
    box-sizing: border-box;
    position: relative;
    .startBtn {
      width: 242px;
      height: 38px;
      line-height: 38px;
      text-align: center;
      border-radius: 4px;
      color: $secondary-color;
      background: url("@/views/aiChat/components/images/newTalk2.png") no-repeat 0% / 100%;
      background-size: 87% 100%;
      margin: 34px 15px 19px 25px;
      cursor: pointer;
    }

    .logList {
      width: 242px;
      border-radius: 6px;
      // border: 1px solid #ffffff14;
      // background: #1d2034ff;

      color: $secondary-color;
      margin: 0 auto 40px;
      overflow-x: hidden;
      overflow-y: auto;
      max-height: calc(var(--vh) * 100 - 280px);
    }

    .logList::-webkit-scrollbar {
      display: none;
      width: 0px;
      background: transparent;
    }

    .logList::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background: #3a3c50;
    }
  }

  .logBox {
    padding: 0 14px 18px 14px;
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    display: flex;
    flex-direction: column;
    .chatTime {
      color: #8197aeff;
      font-size: 12px;
      font-weight: 400;
      font-family: "PingFang SC";
      text-align: justify;
      padding-bottom: 10px;
      padding-top: 10px;
    }
    .logBox:first-child {
      padding-top: 18px;
    }
    .chatItem {
      display: flex;
      cursor: pointer;
      position: relative;
      width: 100%;
      font-size: 14px;
      .middle-dot {
        position: relative;
        display: inline-block;
      }

      .middle-dot::after {
        content: "";
        position: absolute;
        top: 44%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 5px;
        height: 5px;
        border-radius: 50%;
        background-color: $secondary-color;
      }
    }
  }
  .isActive .middle-dot::after {
    background-color: #4892e7 !important;
  }

  .icon {
    width: 18px;
    vertical-align: middle;
  }

  .chatTopic {
    width: auto;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin: 0 2px 0 7px;
    cursor: pointer;
  }

  .isActive .chatTopic {
    color: #4892e7;
  }

  .userList {
    position: absolute;
    bottom: 10px;
    width: 242px;
    left: 50%;
    padding-left: 41px;
    transform: translateX(-50%);
  }

  .userBox {
    display: flex;
    flex-direction: row;
    color: $secondary-color;
    align-items: flex-start;
    padding: 20px 0;
    .avatar {
      margin-right: 6px;
    }
    img {
      width: 18px;
      height: 18px;
    }
    .userBox-text {
      display: flex;
      .feedback {
        cursor: pointer;
        display: flex;
        align-items: center;
        margin-left: 7px;
        a {
          color: #333333;
          text-decoration: none;
          font-size: 12px;
          border-bottom: 1px solid #666666;
        }
      }
      .vertical-line {
        margin: 0 9px;
        margin-top: -0.5px;
        color: #666666;
      }
      .logout {
        cursor: pointer;
        color: #666666;
        font-size: 12px;
      }
      img {
        margin-right: 6px;
        width: 11px;
        height: 11px;
        margin-right: 0;
      }
    }
    span {
      opacity: 1;
      color: #333333;
      font-size: 12px;
      font-weight: 400;
      font-family: "PingFang SC";
      text-align: justify;
      margin-left: 0;
    }
  }

  #scrollbar {
    height: calc(var(--vh) * 100 - 280px);
    display: flex;
    flex-direction: column;
    align-items: center;
    color: $secondary-color;
    margin-bottom: 40px;
    overflow-y: scroll;
    position: relative;

    // #scrollbar::-webkit-scrollbar-thumb {
    //   border-radius: 10px;
    //   background: #f5f5f5;
    // }
  }
  #scrollbar::-webkit-scrollbar {
    width: 0px;
    background: transparent;
  }

  .newDialogue {
    display: flex;
    width: 68.4%;
    position: relative;
    .newBox {
      position: absolute;
      top: 16px;
      left: 10px;
      .titleBox {
        color: $secondary-color;
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        opacity: 1;
        font-size: 26px;
        font-weight: 600;
        font-family: "PingFang SC";
        text-align: justify;
      }
      .h2 {
        font-size: 16px;
        font-weight: 400;
        margin-top: 6px;
        opacity: 1;
        color: $secondary-color;
        font-family: "PingFang SC";
        text-align: justify;
      }
    }

    .qTemplate {
      margin-top: 92px;
      width: 100%;

      .qTitle {
        color: $secondary-color;
      }

      .qContent {
        width: 100%;
        margin: 0 !important;

        // justify-content: flex-start;
        // justify-content: space-around;
        // .qItemBox:nth-child(3n) {
        //   margin-right: 0;
        // }

        .qItemBox {
          // flex-basis: calc(31.2% );
          min-height: 207px;
          // min-width: 200px;
          box-sizing: border-box;
          margin-top: 20px;
          padding-top: 14px;
          border-radius: 6px;
          background: linear-gradient(245.8deg, #edf2fe 0%, #e4effe 94%);
          background-size: 100% 100%;
          .qTop {
            display: flex;
            justify-content: space-between;
            color: $secondary-color;
            margin-left: 22px;
            margin-right: 15px;
            .keyWord {
              opacity: 1;
              color: $secondary-color;
              font-size: 16px;
              font-weight: 600;
              font-family: "PingFang SC";
              text-align: left;
            }
            .editBox {
              display: flex;
              align-items: center;
              cursor: pointer;

              img {
                width: 12px;
                height: 12px;
                margin-right: 6px;
              }
              .editBtn {
                opacity: 1;
                color: #4892e7;
                font-size: 12px;
                font-weight: 400;
                font-family: "PingFang SC";
              }
            }
          }
          .qBottom {
            margin-top: 10px;
            margin-left: 22px;
            margin-right: 14px;
            margin-bottom: 31px;
            display: block;
            opacity: 0.800000011920929;
            color: $secondary-color;
            font-size: 14px;
            font-weight: 400;
            font-family: "PingFang SC";
            text-align:justify;
            letter-spacing: 0.5px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 6; /* 控制显示行数 */
            -webkit-box-orient: vertical;
          }
        }
      }
    }
  }

  .box {
    width: 66.7%;
    // border: 1px solid #ebeaeaff;
    border-radius: 6px;
  }

  .avatar {
    width: 2.5rem;
    height: 2.5rem;
    margin-right: 0.8rem;
  }

  .contextStyle {
    width: 100%;
    box-sizing: border-box;
    color: $secondary-color;

    // border-bottom: 1px solid #ebeaeaff;
  }

  .contextStyle:last-child {
    border-bottom: none;
  }

  .contextStyle1 {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    padding: 1.4rem 0.5rem 1.4rem 1.1rem;
  }
.tag{
        background: #F6FBFF;
        border: solid 1px #2982E8;
    }
  .contextStyle2 {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    padding: 1.1rem;
    // background: url('./images/background2.png');
    background: linear-gradient(0.1deg, #e8f4ffff 0%, #e0f0ffff 100%);
    // background: linear-gradient(180deg, #ffffffff 0%, #ffffff5e 100%);

    border-radius: 6px;
    .contextStyle2Top {
      width: 100%;
      display: flex;
    }
    .contextStyle2Bottom {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
      margin-top: 6px;
      img {
        width: 14px;
        height: 14px;
      }
      span {
        color: #747b82;
      }
    }
  }

  .chatButton {
    width: 50px;
    height: 26.7px;
    border-radius: 4px;
    opacity: 1;
    border: 1px solid #ffffff3d;
    background: #4892e7ff;
    text-align: center;
    cursor: pointer;
    span {
      color: #fff;
    }
  }

  .textareaStyle {
    cursor: not-allowed;
  }

  .footer {
    padding: 2px;
    box-sizing: border-box;
    width: calc((100% - 264px) * 0.669);
    margin: 0px auto;
    position: fixed;
    bottom: 30px;
    left:calc((100% - ((100% - 264px) * 0.669)) / 2 + 132px);
    .footer-wrap-inner{
      background: linear-gradient(to right, #a3eeff, #4892e7);
      padding: 1px;
      overflow: hidden;
    }
  }
  .documents:hover{
    cursor: pointer;
  }
    .documents{
      position: relative;
      margin-right: 10px;
      border-radius: 4px;
      font-size: 12px;
      flex-wrap: wrap;
      padding: 10px;
      color: #666666;
      background-color: #fff;
      .logo{
        width: 22px;
        height: 22px;
        margin-right: 6px;
      }
      .name{
        color: #2F5883;
        font-weight: 400;
        font-size: 14px;
        color: #2F5883;
        white-space: nowrap;
      overflow: hidden;       /* 隐藏溢出内容 */
      text-overflow: ellipsis;/* 溢出显示省略号 */
      max-width: 200px;   
      }
      .size{
        display: flex;
        align-items: center;
        margin-left: 28px;
        font-weight: 400;
        font-size: 12px;
        color: #9AAFC5;
        .el-icon{
          margin-right: 4px;
          font-size: 13px;
        }
      }
      .icon{
        font-size: 12px;
        font-weight: 500;
        position: absolute;
        right: 0;
        display: none;
        top: 0;
        z-index: 100;
        background-color: #E9E9E9;
        border-radius: 50%;
        width: 15px;
        height: 15px;
        line-height: 15px;
        text-align: center;
      }
    }
    .imgs{
      position: relative;
      width: 64px;
      height: 64px;
      margin:0 10px 10px 0;
      .icon{
        font-size: 12px;
        font-weight: 500;
        position: absolute;
        right: -10px;
        display: none;
        top: -10px;
        z-index: 100;
        background-color: #fff;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        line-height: 20px;
        text-align: center;
      }
      img{
        width: 100%;
        height: 100%;
        z-index: 10;
      }
    }

    .footer-wrap {
      display: flex;
    justify-content: space-between;
    align-items: flex-start;
    position: relative;
    width: 100%;
    opacity: 1;
    background: #fff;
    box-sizing: border-box;
    flex-wrap: wrap;
      .files{
        width: 100%;
        position: relative;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        .imgs:hover,.documents:hover{
          cursor: pointer;
          .icon{
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
        .documents{
          position: relative;
          margin: 10px;
          border-radius: 4px;
          font-size: 12px;
          flex-wrap: wrap;
          padding: 10px;
          background-color: #F0F8FF;
          flex-direction: column;
          .logo{
            width: 22px;
            height: 22px;
            margin-right: 6px;
          }
          .name{
            font-weight: 400;
            font-size: 14px;
            color: #2F5883;
            white-space: nowrap;
            overflow: hidden;       /* 隐藏溢出内容 */
            text-overflow: ellipsis;/* 溢出显示省略号 */
            max-width: 200px;   
          }
          .size{
            display: flex;
            align-items: center;
            margin-left: 28px;
            font-weight: 400;
            font-size: 12px;
            color: #9AAFC5;
            .el-icon{
              margin-right: 4px;
              font-size: 10px;
            }
          }
          .icon{
            font-size: 10px;
            font-weight: 500;
            position: absolute;
            right: -7.5px;
            display: none;
            top: -7.5px;
            z-index: 100;
            background-color: #E9E9E9;
            border-radius: 50%;
            width: 15px;
            height: 15px;
            line-height: 15px;
            text-align: center;
          }
        }
        .imgs{
          position: relative;
          width: 64px;
          height: 64px;
          margin:10px 10px 10px;
          .icon{
            font-size: 12px;
            font-weight: 500;
            position: absolute;
            right: -10px;
            display: none;
            top: -10px;
            z-index: 100;
            background-color: #fff;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            line-height: 20px;
            text-align: center;
          }
          img{
            width: 100%;
            height: 100%;
            z-index: 10;
          }
        }
        
      }
      
      .flex1 {
        flex: 1;
        padding-left: 15px;
      text-align: justify;
      padding-right: 10px;
      }

      ::-webkit-scrollbar {
        display: none;
      }
      .transitionBox {
        display: flex;
        .footer-bth {
          background-color: #fff;
          border-radius: 2px;
          border: 1px solid #4892e7cc;
          color: #4892e7;
          border-radius: 2px;
        }
      }
      .fade-enter-active,
      .fade-leave-active {
        opacity: 1;
        transition: opacity 0.3s;
      }
      .fade-enter,
      .fade-leave-active {
        opacity: 0;
      }
      .othertips {
        // position: absolute;
        // z-index: 99999;
        // background-color: #182f3dff;
        // top: -40px;
        // left: -2px;
        // width: 100%;
        // border-radius: 4px;
        // border: 2px solid #999;
        // border-bottom: none;
        width: 100%;
        box-sizing: border-box;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        margin-top: -4px;
        flex-wrap: wrap;
        margin-left: 0px;
        .recommendTitle {
          color: #9cabb6;
          font-size: 14px;
        }
        div {
          font-size: 14px;
          width: 72px;
          //超出省略号
        }
      }

      .chatInput {
        resize: none;
        overflow: auto;
        color: #2f5883ff;
        border: none;
        line-height: 20px;
        height: 25px;
        // flex-grow: 1;
        min-height: 40px;
        max-height: 60px;
        display: block;
        // outline: none;
        text-align: justify;
        font-family: "Microsoft YaHei", sans-serif;
        font-size: 16px;
        background: #fff;
        word-break: break-all;
        width: 100%;
      }
    }
    .btomArea{
      background: #fff;
    display: flex;
    justify-content: flex-end;
    padding: 10px;
     .chatInput {
      resize: none;
      overflow: auto;
      border: none;
      line-height: 25px;
      height: 25px;
      
      display: block;
      // display: inline-block;
      // outline: none;
      text-align: justify;
      font-family: "Microsoft YaHei", sans-serif;
      font-size: 16px;
      word-break: break-all;
      width: 100%;
    }
    }
  
  .sendBtn {
    width: 23px;
    height: 23px;
    vertical-align: middle;
    cursor: pointer;
  }

.chatRefreshButton {
    width: 44px;
    // border-radius: 4px;
    // opacity: 1;
    border: 1px solid #ffffff3d;
    background: #4892e7ff;
    // cursor: pointer;
  }

  .refreshBtn {
    width: 25px;
    height: 23px;
    vertical-align: middle;
    cursor: pointer;
  }


  .linkStyle {
    font-size: 12px;
    font-weight: 400;
    color: #999;
    a {
      color: #4892e7;
      text-decoration: none;
    }
  }
  .change_background {
    display: flex;
    ::v-deep .van-switch__node {
      background: #4795e8;
    }
  }
  span {
    opacity: 1;
    color: #65b0ef;
    font-size: 12px;
    font-weight: 400;
    font-family: "PingFang SC";
    text-align: justify;
  }
}
