<template>
  <div class="bg-[#f3f8fa] p-2 h-full overflow-auto">
    <!-- 搜索 -->
    <div class="flex h-[220px]">
      <!-- 输入框 -->
      <div class="p-4 flex-1 bg-[#fff] rounded-md">
        <div class="text-[16px] font-bold mb-2 text-gray-600">
          请在此输入或粘贴英文
        </div>
        <el-input
          v-model="inputValue"
          :rows="8"
          type="textarea"
          placeholder="请输入内容"
          show-word-limit
          resize="none"
          :maxlength="600"
          @input="handleInput"
        />
      </div>
      <!-- 翻译结果 -->
      <div
        class="p-4 flex-1 rounded-md flex flex-col"
        style="background-color: rgba(57, 126, 195, 0.05)"
        v-if="false"
      >
        <div class="text-[14px] font-bold mb-2 text-gray-600">翻译结果</div>
        <!-- <el-input
          v-model="resultValue"
          :rows="8"
          type="textarea"
          resize="none"
        /> -->
        <div class="flex-1 overflow-auto">{{ result?.textTrans }}</div>
      </div>
    </div>
    <!-- 润色结果 -->
    <div>
      <!-- 工具 -->
      <div class="my-6 items-center">
        <div class="mb-5">
          <div
            v-for="(item, index) in toolBarBtn"
            :key="index"
            class="px-4 py-2 text-white text-[12px] font-bold rounded cursor-pointer mr-2 tool-bar-btn inline-block"
            :class="active == item.value ? 'bg-[#409eff]' : 'bg-gray-400'"
            @click="onActive(item)"
          >
            {{ item.label }}
          </div>
        </div>
        <div class="text-[16px] font-bold text-gray-600 mr-6">AI润色结果</div>
      </div>
      <div v-if="result">
        <!-- 润色分析 -->
        <div class="p-4 bg-white rounded-md mb-4">
          <div class="flex justify-between items-center mb-3">
            <div class="text-[16px] font-bold text-[#3A7EC3]">润色分析</div>
            <!-- 注解 -->
            <div class="flex items-center">
              <span class="text-gray-500 mr-2">注解：</span>
              <div class="flex items-center">
                <div class="flex items-center mr-4">
                  <div
                    class="rounded-1/2 w-3 h-3 bg-[rgba(0,145,255,.2)] mr-2"
                  ></div>
                  <del
                    class="bg-[rgba(0,145,255,.2)] text-[#0091ff] font-bold px-2"
                    >delete</del
                  >
                </div>
                <div class="flex items-center">
                  <div
                    class="rounded-1/2 w-3 h-3 bg-[rgba(255,1,1,.2)] mr-2"
                  ></div>
                  <ins
                    class="bg-[rgba(255,1,1,.2)] text-[#ff0101] font-bold px-2"
                    >insert</ins
                  >
                </div>
              </div>
            </div>
          </div>
          <div class="leading-[22px]" v-html="result.analysis"></div>
        </div>
        <!-- 润色结果 -->
        <div class="p-4 bg-white rounded-md">
          <div>
            <div class="mb-3 flex justify-between items-center">
              <span class="text-[16px] font-bold text-[#3A7EC3]">润色结果</span>
              <el-icon class="cursor-pointer" v-copy="toolBarBtn[0].label"
                ><CopyDocument
              /></el-icon>
            </div>
            <div>{{ result?.polish }}</div>
          </div>
          <el-divider class="bg-[#eaf2f7] !border-top-0" />
          <div>
            <div class="text-[16px] font-bold text-[#3A7EC3] mb-2">
              润色翻译
            </div>
            <div>{{ result.polishTrans }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { CopyDocument } from "@element-plus/icons-vue";
import { medsciAi } from "@/api/index";
import t from "@/utils/polish";
import { encode } from "@/utils/decrypt";
const inputValue = ref("");
const active = ref(null);
const result = ref(null);
const toolBarBtn = ref([
  // { label: "选择模式", value: 0 },
  { label: "经典学术写作风格", value: 0.6 },
  { label: "简洁写作风格", value: 0.9 },
]);

// 选择模式
const onActive = (item) => {
  active.value = item.value;
  getData();
};

// 输入框变化时
const handleInput = () => {
  // console.log('-----')
}
// 获取数据
const getData = () => {
  let isChina = (s) => {
    var patrn = /[\u4E00-\u9FA5]|[\uFE30-\uFFA0]/gi;
    if (!patrn.exec(s)) {
      return false;
    } else {
      return true;
    }
  }
  if (isChina(inputValue.value) || !inputValue.value) {
    return ElMessage.warning("请输入英文内容");
  }
  medsciAi("polish", {
    text: inputValue.value,
    temperature: active.value,
    isTrans: 1,
  }).then((res) => {
    result.value = res.data.result;
    result.value.analysis = t(result.value.text, result.value.polish);
    getResult();
  });
};
// 获取润色后的翻译结果
const getResult = () => {
  medsciAi("polish", {
    text: "r",
    tr: encode(result.value.polish).split("").reverse().join(""),
    temperature: active.value,
    isTrans: 0,
    remain: 1,
  }).then((res) => {
    result.value.polishTrans = res.data;
  });
};
// onMounted(() => {
//   return;

//   getData();
// });
</script>

<style lang="scss" scoped>
:deep(.el-textarea__inner) {
  outline: none;
  border: none;
  resize: none;
  box-shadow: none;
  background: none;
  padding: 0;
}
.tool-bar-btn:hover {
  background: #409eff;
}
</style>
