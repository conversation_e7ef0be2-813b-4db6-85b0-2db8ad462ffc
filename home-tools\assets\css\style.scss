@charset "utf-8";

:root {
  font-size: 14px;
}
html,body,#app {
  height: 100%;
}

.content {
  max-width: 1140px;
  min-width: 980px;
  margin: 0 auto;
}
.el-loading-text{
  text-align: center !important;
}
.el-checkbox {
  margin-bottom: 0 !important; 
}

em {
  color: red
}

// 全局样式
* {
  margin: 0;
  padding: 0;
  // font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB",
  //   "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
  font-family: "思源", <PERSON>, <PERSON><PERSON>, "SC-Bold";
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
}

// body样式
body {
  // min-width: 1200px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

// 让滚动更丝滑
html,
body {
  scroll-behavior: smooth;
  -webkit-scroll-behavior: smooth;
}

// a标签
a {
  text-decoration: none;
  color: #333;
}

// 去掉input，button选中样式
input,
button {
  outline: none;
}

button {
  cursor: pointer;
  border: none;
}

// 去掉ul li默认样式
ul,
li {
  list-style: none;
}

// 使i标签字体正常
i {
  font-style: normal;
}
