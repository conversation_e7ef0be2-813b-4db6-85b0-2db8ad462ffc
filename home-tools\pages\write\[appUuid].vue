<template>
  <div>
    <iframe :src="url"></iframe>
    <div id="app" data-v-app="">
      <div data-v-ca32f695="">
        <div data-v-b2cf83f1="" data-v-ca32f695="" class="assistant-container">
          <div data-v-b2cf83f1="" class="assistant-icon">
            <!-- 这里可以放置小助理的图标，例如使用font-awesome --><img
              data-v-b2cf83f1=""
              src="@/assets/imgs/kefu.png"
              class="fas fa-user-astronaut"
              alt="客服"
            />
          </div>
          <div data-v-b2cf83f1="" class="qr-code">
            <!-- 这里放置二维码图片 --><img
              data-v-b2cf83f1=""
              src="@/assets/imgs/qrcode.png"
              alt="QR Code"
            />
            扫码添加学术老师
          </div>
        </div>
        <header data-v-ca32f695="" class="container">
          <div data-v-ca32f695="" class="left">
            <h3 data-v-ca32f695="">
              <img
                data-v-ca32f695=""
                class="logo"
                src="https://img.medsci.cn/202412/8a5663cb16a84c45b9c8c5db4eda0075-6pyGtTcj0asd.png"
                alt=""
              />
              <span data-v-ca32f695="" class="title">()</span>
            </h3>
            <button
              data-v-7a4b5c57=""
              aria-disabled="false"
              type="button"
              class="el-button el-button--primary is-plain is-round btn"
            >
              <!--v-if--><span class=""
                >{{$t('tool.AI历史对话')}}<img
                  data-v-7a4b5c57=""
                  src="@/assets/imgs/right.png"
                  alt=""
                  style="width: 10px; height: 10px; margin-left: 6px"
              /></span></button
            ><!--v-if--><!--v-if--><!--teleport start--><!--teleport end-->
            <div class="el-overlay" style="z-index: 2002; display: none">
              <div
                role="dialog"
                aria-modal="true"
                aria-label="关于“”的全部对话"
                aria-describedby="el-id-1541-1"
                class="el-overlay-dialog"
              ></div>
            </div>
            <button
              data-v-ca32f695=""
              aria-disabled="false"
              type="button"
              class="el-button btn"
            >
              <!--v-if--><span class=""
                ><svg
                  data-v-ca32f695=""
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 1024 1024"
                  style="width: 1em; height: 1em; margin-right: 8px"
                >
                  <path
                    fill="currentColor"
                    d="M480 480V128a32 32 0 0 1 64 0v352h352a32 32 0 1 1 0 64H544v352a32 32 0 1 1-64 0V544H128a32 32 0 0 1 0-64z"
                  ></path></svg
                >{{$t('tool.新对话')}}</span
              >
            </button>
          </div>
          <div data-v-ca32f695="" class="right">
            <!-- <a href="https://www.medsci.cn/messagePush">
          <img class="messsageImg"
            src="https://static.medsci.cn/public-image/ms-image/4ec68590-27ae-11ee-aed8-05e366306843_通知中心@2x.png"
            alt="" />
        </a> --><a
              data-v-ca32f695=""
              class="backImg"
              href="http://localhost:3000"
              >{{$t("tool.backtohome")}}</a
            ><a
              data-v-ca32f695=""
              href="javascript: void(0)"
              class="ms-link"
              style="margin: 0px 15px"
              >{{$t("tool.signIn")}}</a
            ><!-- <a href="https://www.medsci.cn/message/list.do" class="ms-link" ms-statis="link">咨询</a> -->
          </div>
        </header>
        <main data-v-ca32f695="">
          <div
            data-v-82531f42=""
            id="umo-editor-f82e"
            class="umo-editor-container toolbar-ribbon"
            style="height: calc(-60px + 100vh)"
          >
            <header data-v-82531f42="" class="umo-toolbar">
              <div
                data-v-a821cdc8=""
                data-v-82531f42=""
                class="toolbar-container"
              >
                <div data-v-3a096f28="" data-v-a821cdc8="" class="ribbon-menu">
                  <div data-v-3a096f28="" class="ribbon-tabs">
                    <div data-v-3a096f28="" class="tabs-item active">{{$t('toolbar.base')}}</div>
                    <div data-v-3a096f28="" class="tabs-item">{{$t('toolbar.insert')}}</div>
                    <div data-v-3a096f28="" class="tabs-item">{{$t('toolbar.table')}}</div>
                    <div data-v-3a096f28="" class="tabs-item">{{$t('toolbar.tools')}}</div>
                    <div data-v-3a096f28="" class="tabs-item">{{$t('toolbar.page')}}</div>
                    <div data-v-3a096f28="" class="tabs-item">{{$t('toolbar.export')}}</div>
                  </div>
                  <div
                    data-v-6317703f=""
                    data-v-3a096f28=""
                    class="scrollable-container"
                  >
                    <!---->
                    <div data-v-6317703f="" class="scrollable-content">
                      <div data-v-3a096f28="" class="ribbon-container">
                        <div data-v-3a096f28="" class="virtual-group">
                          <div data-v-3a096f28="" class="virtual-group-row">
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s umo-is-disabled umo-button--shape-square menu-button"
                                type="button"
                                disabled=""
                                href=""
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-undo"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">{{$t('toolbar.undo')}}</p>
                                    <!---->
                                  </div></span
                                >
                              </button>
                            </div>
                            <!---->
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s umo-is-disabled umo-button--shape-square menu-button"
                                type="button"
                                disabled=""
                                href=""
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-redo"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">{{$t('toolbar.redo')}}</p>
                                    <!---->
                                  </div></span
                                >
                              </button>
                            </div>
                            <!---->
                          </div>
                          <div data-v-3a096f28="" class="virtual-group-row">
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s umo-button--shape-square menu-button"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-select-all"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">{{$t('toolbar.selectAll')}}</p>
                                    <!---->
                                  </div></span
                                >
                              </button>
                            </div>
                            <!---->
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s umo-button--shape-square menu-button"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-clear-format"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">
                                      {{$t('toolbar.clearFormat')}}
                                    </p>
                                    <!---->
                                  </div></span
                                >
                              </button>
                            </div>
                            <!---->
                          </div>
                        </div>
                        <div data-v-3a096f28="" class="virtual-group">
                          <div data-v-3a096f28="" class="virtual-group-row">
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <div
                                data-v-cb3e9e3e=""
                                class="umo-select__wrap"
                                placement="bottom-left"
                                style="width: 144px"
                              >
                                <div class="umo-select-input umo-select">
                                  <div class="umo-input__wrap">
                                    <div
                                      class="umo-input umo-size-s umo-input--suffix"
                                    >
                                      <!----><!----><input
                                        class="umo-input__inner"
                                        placeholder="请选择"
                                        type="text"
                                        spellcheck="false"
                                        :value="$t('base.fontFamily.default')"
                                      /><!----><!----><!----><span
                                        class="umo-input__suffix umo-input__suffix-icon"
                                        ><svg
                                          class="umo-fake-arrow umo-select__right-icon"
                                          width="16"
                                          height="16"
                                          viewBox="0 0 16 16"
                                          fill="none"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            d="M3.75 5.7998L7.99274 10.0425L12.2361 5.79921"
                                            stroke="black"
                                            stroke-opacity="0.9"
                                            stroke-width="1.3"
                                          ></path></svg
                                      ></span>
                                    </div>
                                    <!---->
                                  </div>
                                  <!----><!---->
                                </div>
                              </div>
                            </div>
                            <!---->
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <div
                                data-v-cb3e9e3e=""
                                class="umo-select__wrap"
                                placement="bottom-left"
                                style="width: 80px"
                              >
                                <div class="umo-select-input umo-select">
                                  <div class="umo-input__wrap">
                                    <div
                                      class="umo-input umo-size-s umo-input--suffix"
                                    >
                                      <!----><!----><input
                                        class="umo-input__inner"
                                        :placeholder="$t('base.fontSize.text')"
                                        type="text"
                                        spellcheck="false"
                                        value="默认"
                                      /><!----><!----><!----><span
                                        class="umo-input__suffix umo-input__suffix-icon"
                                        ><svg
                                          class="umo-fake-arrow umo-select__right-icon"
                                          width="16"
                                          height="16"
                                          viewBox="0 0 16 16"
                                          fill="none"
                                          xmlns="http://www.w3.org/2000/svg"
                                        >
                                          <path
                                            d="M3.75 5.7998L7.99274 10.0425L12.2361 5.79921"
                                            stroke="black"
                                            stroke-opacity="0.9"
                                            stroke-width="1.3"
                                          ></path></svg
                                      ></span>
                                    </div>
                                    <!---->
                                  </div>
                                  <!----><!---->
                                </div>
                              </div>
                            </div>
                            <!---->
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s umo-button--shape-square menu-button"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-font-size-increase"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">
                                      {{$t('base.fontSize.increase')}}
                                    </p>
                                    <!---->
                                  </div></span
                                >
                              </button>
                            </div>
                            <!---->
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s umo-button--shape-square menu-button"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-font-size-decrease"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">
                                      {{$t('base.fontSize.increase')}}
                                    </p>
                                    <!---->
                                  </div></span
                                >
                              </button>
                            </div>
                            <!---->
                          </div>
                          <div data-v-3a096f28="" class="virtual-group-row">
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s umo-button--shape-square menu-button"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-bold"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">{{$t('base.bold')}}</p>
                                    <!---->
                                  </div></span
                                >
                              </button>
                            </div>
                            <!---->
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s umo-button--shape-square menu-button"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-italic"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">{{$t('base.italic')}}</p>
                                    <!---->
                                  </div></span
                                >
                              </button>
                            </div>
                            <!---->
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s umo-button--shape-square menu-button"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-underline"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">
                                      {{$t('base.underline')}}
                                    </p>
                                    <!---->
                                  </div></span
                                >
                              </button>
                            </div>
                            <!---->
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s umo-button--shape-square menu-button"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-strike"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">
                                      {{$t('base.strike')}}
                                    </p>
                                    <!---->
                                  </div></span
                                >
                              </button>
                            </div>
                            <!---->
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s umo-button--shape-square menu-button"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-subscript"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">{{$t('base.subscript')}}</p>
                                    <!---->
                                  </div></span
                                >
                              </button>
                            </div>
                            <!---->
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s umo-button--shape-square menu-button"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-superscript"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">{{$t('base.superscript')}}</p>
                                    <!---->
                                  </div></span
                                >
                              </button>
                            </div>
                            <!---->
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s menu-button has-arrow"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <div
                                      data-v-7657d858=""
                                      class="current-color"
                                    ></div>
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-color"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">
                                      {{$t('base.color')}}
                                    </p>
                                    <!---->
                                  </div>
                                  <span
                                    data-v-cb3e9e3e=""
                                    class="icon-arrow handle"
                                    ><svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-arrow-down"
                                        fill="currentColor"
                                      ></use></svg></span
                                  ><!----></span
                                >
                              </button>
                            </div>
                            <!---->
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s menu-button has-arrow"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-a1a819f6=""
                                      class="icon icon-background-color"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-background-color"
                                        fill="currentColor"
                                      ></use></svg
                                    ><!---->
                                    <p data-v-cb3e9e3e="" class="text">
                                      {{$t('base.bgColor')}}
                                    </p>
                                    <!---->
                                  </div>
                                  <span
                                    data-v-cb3e9e3e=""
                                    class="icon-arrow handle"
                                    ><svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-arrow-down"
                                        fill="currentColor"
                                      ></use></svg></span
                                  ><!----></span
                                >
                              </button>
                            </div>
                            <!---->
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s menu-button has-arrow"
                                overlay-class-name="highlight-dropdown"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-6a2c0b06=""
                                      class="icon icon-highlight"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-highlight"
                                        fill="currentColor"
                                      ></use></svg
                                    ><!---->
                                    <p data-v-cb3e9e3e="" class="text">
                                      {{$t('base.highlight.text')}}
                                    </p>
                                    <!---->
                                  </div>
                                  <span
                                    data-v-cb3e9e3e=""
                                    class="icon-arrow handle"
                                    ><svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-arrow-down"
                                        fill="currentColor"
                                      ></use></svg></span
                                  ><!----></span
                                >
                              </button>
                            </div>
                            <!---->
                          </div>
                        </div>
                        <div data-v-3a096f28="" class="virtual-group">
                          <div data-v-3a096f28="" class="virtual-group-row">
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s menu-button has-arrow"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-ordered-list"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">
                                      {{$t('list.ordered.text')}}
                                    </p>
                                    <!---->
                                  </div>
                                  <span
                                    data-v-cb3e9e3e=""
                                    class="icon-arrow handle"
                                    ><svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-arrow-down"
                                        fill="currentColor"
                                      ></use></svg></span
                                  ><!----></span
                                >
                              </button>
                            </div>
                            <!---->
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s menu-button has-arrow"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-bullet-list"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">
                                      {{$t('list.bullet.text')}}
                                    </p>
                                    <!---->
                                  </div>
                                  <span
                                    data-v-cb3e9e3e=""
                                    class="icon-arrow handle"
                                    ><svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-arrow-down"
                                        fill="currentColor"
                                      ></use></svg></span
                                  ><!----></span
                                >
                              </button>
                            </div>
                            <!---->
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s menu-button has-arrow"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-task-list"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">
                                      {{$t('list.task.text')}}
                                    </p>
                                    <!---->
                                  </div>
                                  <span
                                    data-v-cb3e9e3e=""
                                    class="icon-arrow handle"
                                    ><svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-arrow-down"
                                        fill="currentColor"
                                      ></use></svg></span
                                  ><!----></span
                                >
                              </button>
                            </div>
                            <!---->
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s umo-button--shape-square menu-button"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-indent"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">
                                      {{$t('base.indent')}}
                                    </p>
                                    <!---->
                                  </div></span
                                >
                              </button>
                            </div>
                            <!---->
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s umo-button--shape-square menu-button"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-outdent"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">
                                      {{$t('base.outdent')}}
                                    </p>
                                    <!---->
                                  </div></span
                                >
                              </button>
                            </div>
                            <!---->
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s menu-button has-arrow"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-line-height"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">{{$t('base.lineHeight.text')}}</p>
                                    <!----><span
                                      data-v-cb3e9e3e=""
                                      class="icon-arrow"
                                      ><svg
                                        data-v-cb3e9e3e=""
                                        class="icon"
                                        aria-hidden="true"
                                        width="1em"
                                        height="1em"
                                      >
                                        <use
                                          xlink:href="#icon-arrow-down"
                                          fill="currentColor"
                                        ></use></svg
                                    ></span>
                                  </div>
                                  <!----></span
                                ></button
                              ><!---->
                            </div>
                            <!---->
                          </div>
                          <div data-v-3a096f28="" class="virtual-group-row">
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s umo-button--shape-square menu-button active"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-align-left"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">
                                      {{$t('base.align.left')}}
                                    </p>
                                    <!---->
                                  </div></span
                                >
                              </button>
                            </div>
                            <!---->
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s umo-button--shape-square menu-button"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-align-center"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">
                                      {{$t('base.align.center')}}
                                    </p>
                                    <!---->
                                  </div></span
                                >
                              </button>
                            </div>
                            <!---->
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s umo-button--shape-square menu-button"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-align-right"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">
                                      {{$t('base.align.right')}}
                                    </p>
                                    <!---->
                                  </div></span
                                >
                              </button>
                            </div>
                            <!---->
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s umo-button--shape-square menu-button"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-align-justify"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">
                                      {{$t('base.align.justify')}}
                                    </p>
                                    <!---->
                                  </div></span
                                >
                              </button>
                            </div>
                            <!---->
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s umo-button--shape-square menu-button"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-align-distributed"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">
                                      {{$t('base.align.distributed')}}
                                    </p>
                                    <!---->
                                  </div></span
                                >
                              </button>
                            </div>
                            <!---->
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s umo-button--shape-square menu-button"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-quote"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">{{$t('base.quote')}}</p>
                                    <!---->
                                  </div></span
                                >
                              </button>
                            </div>
                            <!---->
                            <div data-v-cb3e9e3e="" class="menu-button-wrap">
                              <button
                                data-v-cb3e9e3e=""
                                class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s umo-button--shape-square menu-button"
                                type="button"
                                href=""
                                tabindex="0"
                              >
                                <span class="umo-button__text"
                                  ><div
                                    data-v-cb3e9e3e=""
                                    class="button-content"
                                  >
                                    <svg
                                      data-v-cb3e9e3e=""
                                      class="icon"
                                      aria-hidden="true"
                                      width="1em"
                                      height="1em"
                                    >
                                      <use
                                        xlink:href="#icon-code"
                                        fill="currentColor"
                                      ></use>
                                    </svg>
                                    <p data-v-cb3e9e3e="" class="text">{{$t('base.code')}}</p>
                                    <!---->
                                  </div></span
                                >
                              </button>
                            </div>
                            <!---->
                          </div>
                        </div>
                        <div data-v-3a096f28="" class="virtual-group">
                          <div
                            data-v-ff1c19d1=""
                            data-v-3a096f28=""
                            class="toolbar-headding"
                            disabled="false"
                          >
                            <div data-v-ff1c19d1="" class="heading-container">
                              <div data-v-ff1c19d1="" class="card active">
                                <div data-v-ff1c19d1="" class="title text">
                                  {{$t('base.heading.paragraph')}}
                                </div>
                                <div data-v-ff1c19d1="" class="subtitle">
                                  text
                                </div>
                              </div>
                              <div data-v-ff1c19d1="" class="card">
                                <div data-v-ff1c19d1="" class="title h1">
                                  {{$t('base.heading.text', { level: 1 })}}
                                </div>
                                <div data-v-ff1c19d1="" class="subtitle">
                                  h1
                                </div>
                              </div>
                              <div data-v-ff1c19d1="" class="card">
                                <div data-v-ff1c19d1="" class="title h2">
                                  {{$t('base.heading.text', { level: 2 })}}
                                </div>
                                <div data-v-ff1c19d1="" class="subtitle">
                                  h2
                                </div>
                              </div>
                              <div data-v-ff1c19d1="" class="card">
                                <div data-v-ff1c19d1="" class="title h3">
                                  {{$t('base.heading.text', { level:3 })}}
                                </div>
                                <div data-v-ff1c19d1="" class="subtitle">
                                  h3
                                </div>
                              </div>
                              <!----><!---->
                              <div data-v-ff1c19d1="" class="arrow">
                                <svg
                                  data-v-ff1c19d1=""
                                  class="icon"
                                  aria-hidden="true"
                                  width="1em"
                                  height="1em"
                                >
                                  <use
                                    xlink:href="#icon-arrow-down"
                                    fill="currentColor"
                                  ></use>
                                </svg>
                              </div>
                              <!---->
                            </div>
                          </div>
                        </div>
                        <div data-v-3a096f28="" class="virtual-group">
                          <div data-v-cb3e9e3e="" class="menu-button-wrap">
                            <button
                              data-v-cb3e9e3e=""
                              class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s umo-button--shape-square menu-button huge show-text active"
                              type="button"
                              href=""
                              tabindex="0"
                            >
                              <span class="umo-button__text"
                                ><div data-v-cb3e9e3e="" class="button-content">
                                  <svg
                                    data-v-cb3e9e3e=""
                                    class="icon"
                                    aria-hidden="true"
                                    width="1em"
                                    height="1em"
                                  >
                                    <use
                                      xlink:href="#icon-markdown"
                                      fill="currentColor"
                                    ></use>
                                  </svg>
                                  <p data-v-cb3e9e3e="" class="text">
                                    Markdown
                                  </p>
                                  <!---->
                                </div></span
                              >
                            </button>
                          </div>
                          <!---->
                          <div data-v-cb3e9e3e="" class="menu-button-wrap">
                            <button
                              data-v-cb3e9e3e=""
                              class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s umo-button--shape-square menu-button huge show-text"
                              type="button"
                              href=""
                              tabindex="0"
                            >
                              <span class="umo-button__text"
                                ><div data-v-cb3e9e3e="" class="button-content">
                                  <svg
                                    data-v-cb3e9e3e=""
                                    class="icon"
                                    aria-hidden="true"
                                    width="1em"
                                    height="1em"
                                  >
                                    <use
                                      xlink:href="#icon-search-replace"
                                      fill="currentColor"
                                    ></use>
                                  </svg>
                                  <p data-v-cb3e9e3e="" class="text">
                                    {{$t('search.text')}}
                                  </p>
                                  <!---->
                                </div></span
                              >
                            </button>
                          </div>
                          <!---->
                        </div>
                        <div data-v-3a096f28="" class="virtual-group">
                          <div data-v-cb3e9e3e="" class="menu-button-wrap">
                            <button
                              data-v-cb3e9e3e=""
                              class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s umo-button--shape-square menu-button huge show-text"
                              type="button"
                              href=""
                              tabindex="0"
                            >
                              <span class="umo-button__text"
                                ><div data-v-cb3e9e3e="" class="button-content">
                                  <svg
                                    data-v-cb3e9e3e=""
                                    class="icon"
                                    aria-hidden="true"
                                    width="1em"
                                    height="1em"
                                  >
                                    <use
                                      xlink:href="#icon-print"
                                      fill="currentColor"
                                    ></use>
                                  </svg>
                                  <p data-v-cb3e9e3e="" class="text">{{$t('print.text')}}</p>
                                  <!---->
                                </div></span
                              >
                            </button>
                          </div>
                          <!---->
                        </div>
                        <div
                          data-v-3a096f28=""
                          class="virtual-group is-slot"
                        ></div>
                        <!----><!----><!----><!----><!---->
                      </div>
                    </div>
                    <!---->
                  </div>
                </div>
                <!----><!---->
                <div data-v-a821cdc8="" class="toolbar-actions ribbon">
                  <button
                    data-v-a821cdc8=""
                    class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s button"
                    type="button"
                    href=""
                    tabindex="0"
                  >
                    <span class="umo-button__text"
                      ><svg
                        data-v-a821cdc8=""
                        class="icon"
                        aria-hidden="true"
                        width="1em"
                        height="1em"
                      >
                        <use
                          xlink:href="#icon-expand-down"
                          fill="currentColor"
                        ></use></svg
                      ><span data-v-a821cdc8="" class="button-text"
                        >{{$t('toolbar.toggle')}}</span
                      ></span
                    ></button
                  ><!---->
                </div>
              </div>
            </header>
            <main data-v-82531f42="" class="umo-main">
              <div data-v-d3dec150="" data-v-82531f42="" class="page-container">
                <div
                  data-v-72775be4=""
                  data-v-d3dec150=""
                  class="toc-container"
                >
                  <div data-v-72775be4="" class="toc-title">
                    <svg
                      data-v-72775be4=""
                      class="icon icon-toc"
                      aria-hidden="true"
                      width="1em"
                      height="1em"
                    >
                      <use xlink:href="#icon-toc" fill="currentColor"></use>
                    </svg>
                    {{$t('toc.title')}}
                    <div data-v-72775be4="" class="umo-dialog__close">
                      <svg
                        data-v-72775be4=""
                        class="icon"
                        aria-hidden="true"
                        width="1em"
                        height="1em"
                      >
                        <use xlink:href="#icon-close" fill="currentColor"></use>
                      </svg>
                    </div>
                  </div>
                  <div data-v-72775be4="" class="toc-content umo-scrollbar">
                    <div data-v-72775be4="" class="toc-empty">{{$t('toc.empty')}}</div>
                  </div>
                </div>
                <div
                  data-v-d3dec150=""
                  class="zoomable-container umo-scrollbar"
                >
                  <div
                    data-v-1cce3635=""
                    class="ai-search"
                    style="width: calc(583.323px)"
                  >
                    <div data-v-1cce3635="" class="ai-search-container">
                      <div data-v-1cce3635="" class="ai-search-input">
                        <svg
                          data-v-1cce3635=""
                          class="icon"
                          aria-hidden="true"
                          width="24"
                          height="24"
                          style="margin: 2px 0px 0px 5px"
                        >
                          <use
                            xlink:href="#icon-robot"
                            fill="currentColor"
                          ></use>
                        </svg>
                        <div
                          data-v-30d8d76e=""
                          data-v-1cce3635=""
                          class="textarea-container"
                          style="flex: 1 1 0%"
                        >
                          <textarea
                            data-v-30d8d76e=""
                            :placeholder="$t('tool.shiftEnter')"
                            class="resizable-textarea-qd9deuy"
                            rows="1"
                            style="height: 28px; overflow-y: hidden"
                          ></textarea
                          ><!----><!---->
                        </div>
                        <svg
                          data-v-1cce3635=""
                          class="icon icon-query"
                          aria-hidden="true"
                          width="24"
                          height="24"
                        >
                          <use
                            xlink:href="#icon-start"
                            fill="currentColor"
                          ></use></svg
                        ><!----><svg
                          data-v-1cce3635=""
                          class="icon icon-query"
                          aria-hidden="true"
                          width="24"
                          height="24"
                        >
                          <use
                            xlink:href="#icon-close1"
                            fill="currentColor"
                          ></use></svg
                        ><!---->
                      </div>
                      <!---->
                    </div>
                  </div>
                  <!----><!---->
                  <div
                    data-v-d3dec150=""
                    class="zoomable-content"
                    style="
                      width: calc(793.701px);
                      min-height: calc(1111.18px);
                      height: 1111px;
                    "
                  >
                    <div
                      data-v-d3dec150=""
                      class="umo-watermark page-content"
                      style="
                        position: relative;
                        overflow: hidden;
                        width: 21cm;
                        min-height: 29.4cm;
                        transform: scale(1);
                        padding: 2.54cm 3.18cm;
                        background: rgb(255, 255, 255);
                      "
                    >
                      <div
                        spellcheck="true"
                        class="editor-container"
                        aria-expanded="false"
                        style="line-height: 1.5"
                      >
                        <div
                          contenteditable="true"
                          translate="no"
                          class="tiptap ProseMirror umo-editor"
                          tabindex="0"
                          v-html="remard"
                        >
                        </div>
                      </div>
                      <div
                        class="block-menu-hander"
                        style="transform: translate(-38px, 0px)"
                      >
                        <div
                          data-v-cb3e9e3e=""
                          class="menu-button-wrap block-menu-button"
                        >
                          <button
                            data-v-cb3e9e3e=""
                            class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s umo-button--shape-square menu-button block-menu-button"
                            type="button"
                            href=""
                            tabindex="0"
                          >
                            <span class="umo-button__text"
                              ><div data-v-cb3e9e3e="" class="button-content">
                                <svg
                                  data-v-cb3e9e3e=""
                                  class="icon"
                                  aria-hidden="true"
                                  width="1em"
                                  height="1em"
                                >
                                  <use
                                    xlink:href="#icon-block-menu"
                                    fill="currentColor"
                                  ></use>
                                </svg>
                                <p data-v-cb3e9e3e="" class="text"></p>
                                <!---->
                              </div></span
                            >
                          </button>
                        </div>
                        <!---->
                      </div>
                      <!---->
                      <div
                        style="
                          position: absolute;
                          inset: 0px;
                          width: 100%;
                          height: 100%;
                          background-size: 320px;
                          pointer-events: none;
                          background-repeat: repeat;
                          background-image: url('data:image/png;base64,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');
                          animation: auto ease 0s 1 normal none running none;
                        "
                      ></div>
                    </div>
                  </div>
                </div>
                <!----><!--teleport start--><!--teleport end--><button
                  data-v-d3dec150=""
                  type="button"
                  class="umo-back-top umo-back-top--theme-light umo-back-top--square umo-size-s"
                  style="inset-inline-end: 25px; inset-block-end: 30px"
                >
                  <svg
                    fill="none"
                    viewBox="0 0 24 24"
                    width="1em"
                    height="1em"
                    class="t-icon t-icon-backtop umo-back-top__icon"
                    style="font-size: 24px"
                  >
                    <path
                      fill="currentColor"
                      d="M4 4h16v2H4V4zm8 3.59l6.91 6.91-1.41 1.41-4.5-4.5V21h-2v-9.59l-4.5 4.5-1.41-1.41L12 7.59z"
                    ></path></svg
                  ><span class="umo-back-top__text">TOP</span></button
                ><!--teleport start--><!--teleport end--><!--teleport start--><!--teleport end--><!--teleport start--><!--teleport end-->
              </div>
            </main>
            <footer data-v-82531f42="" class="umo-footer">
              <div data-v-60bebfec="" class="status-bar">
                <div data-v-60bebfec="" class="bar-left">
                  <button
                    data-v-60bebfec=""
                    class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s bar-button active"
                    type="button"
                    href=""
                    tabindex="0"
                  >
                    <span class="umo-button__text"
                      ><svg
                        data-v-60bebfec=""
                        class="icon"
                        aria-hidden="true"
                        width="1em"
                        height="1em"
                      >
                        <use xlink:href="#icon-toc" fill="red"></use></svg
                    ></span></button
                  ><!----><button
                    data-v-60bebfec=""
                    class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s bar-button active"
                    type="button"
                    href=""
                    tabindex="0"
                  >
                    <span class="umo-button__text"
                      ><svg
                        data-v-60bebfec=""
                        class="icon"
                        aria-hidden="true"
                        width="1em"
                        height="1em"
                      >
                        <use
                          xlink:href="#icon-spellcheck"
                          fill="red"
                        ></use></svg
                    ></span></button
                  ><!----><button
                    data-v-60bebfec=""
                    class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s bar-button"
                    type="button"
                    href=""
                    tabindex="0"
                  >
                    <span class="umo-button__text"
                      ><svg
                        data-v-60bebfec=""
                        class="icon"
                        aria-hidden="true"
                        width="1em"
                        height="1em"
                      >
                        <use
                          xlink:href="#icon-shortcut"
                          fill="currentColor"
                        ></use></svg
                    ></span></button
                  ><!----><button
                    data-v-60bebfec=""
                    class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s bar-button"
                    type="button"
                    href=""
                    tabindex="0"
                  >
                    <span class="umo-button__text"
                      ><svg
                        data-v-60bebfec=""
                        class="icon"
                        aria-hidden="true"
                        width="1em"
                        height="1em"
                      >
                        <use
                          xlink:href="#icon-clear-cache"
                          fill="currentColor"
                        ></use></svg
                    ></span></button
                  ><!---->
                  <div data-v-60bebfec="" class="bar-split"></div>
                  <div data-v-60bebfec="" class="bar-split"></div>
                  <div data-v-60bebfec="" class="simple-text word-count">
                    已输入 0 字符，
                    <!---->
                    已选中 0 字符
                  </div>
                </div>
                <div data-v-60bebfec="" class="bar-center">
                  内容由 AI 生成, 仅供参考
                </div>
                <div data-v-60bebfec="" class="bar-right">
                  <button
                    data-v-60bebfec=""
                    class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s bar-button"
                    type="button"
                    href=""
                    tabindex="0"
                  >
                    <span class="umo-button__text"
                      ><svg
                        data-v-60bebfec=""
                        class="icon"
                        aria-hidden="true"
                        width="1em"
                        height="1em"
                      >
                        <use
                          xlink:href="#icon-preview"
                          fill="currentColor"
                        ></use></svg
                    ></span></button
                  ><!----><button
                    data-v-60bebfec=""
                    class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s bar-button"
                    type="button"
                    href=""
                    tabindex="0"
                  >
                    <span class="umo-button__text"
                      ><svg
                        data-v-60bebfec=""
                        class="icon"
                        aria-hidden="true"
                        width="1em"
                        height="1em"
                      >
                        <use
                          xlink:href="#icon-full-screen"
                          fill="currentColor"
                        ></use></svg
                    ></span></button
                  ><!---->
                  <div data-v-60bebfec="" class="bar-split"></div>
                  <div data-v-60bebfec="" class="zoom-level-bar">
                    <button
                      data-v-60bebfec=""
                      class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s bar-button"
                      type="button"
                      href=""
                      tabindex="0"
                    >
                      <span class="umo-button__text"
                        ><svg
                          data-v-60bebfec=""
                          class="icon"
                          aria-hidden="true"
                          width="1em"
                          height="1em"
                        >
                          <use
                            xlink:href="#icon-minus"
                            fill="currentColor"
                          ></use></svg
                      ></span></button
                    ><!---->
                    <div
                      data-v-60bebfec=""
                      class="umo-slider__container"
                      aria-valuetext="100"
                    >
                      <div
                        class="umo-slider"
                        role="slider"
                        aria-valuemin="20"
                        aria-valuemax="500"
                        aria-orientation="horizontal"
                        aria-disabled="false"
                        tooltip-props="[object Object]"
                      >
                        <div class="umo-slider__rail">
                          <div
                            class="umo-slider__track"
                            style="width: 16.6667%; left: 0%"
                          ></div>
                          <div
                            class="umo-slider__button-wrapper"
                            tabindex="0"
                            show-tooltip="true"
                            disabled="false"
                            style="left: 16.6667%"
                          >
                            <div class="umo-slider__button"></div>
                            <!---->
                          </div>
                          <!----><!----><!---->
                        </div>
                      </div>
                      <!---->
                    </div>
                    <button
                      data-v-60bebfec=""
                      class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s bar-button"
                      type="button"
                      href=""
                      tabindex="0"
                    >
                      <span class="umo-button__text"
                        ><svg
                          data-v-60bebfec=""
                          class="icon"
                          aria-hidden="true"
                          width="1em"
                          height="1em"
                        >
                          <use
                            xlink:href="#icon-plus"
                            fill="currentColor"
                          ></use></svg
                      ></span></button
                    ><!----><button
                      data-v-60bebfec=""
                      class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s bar-button"
                      type="button"
                      href=""
                      tabindex="0"
                    >
                      <span class="umo-button__text"
                        ><svg
                          data-v-60bebfec=""
                          class="icon"
                          aria-hidden="true"
                          width="1em"
                          height="1em"
                        >
                          <use
                            xlink:href="#icon-auto-width"
                            fill="currentColor"
                          ></use></svg
                      ></span></button
                    ><!----><button
                      data-v-60bebfec=""
                      class="umo-button umo-button--variant-text umo-button--theme-default umo-size-s bar-button zoom-button"
                      type="button"
                      href=""
                      tabindex="0"
                    >
                      <span class="umo-button__text">100% </span></button
                    ><!---->
                  </div>
                </div>
              </div>
              <!---->
            </footer>
            <!----><!---->
            <div
              class="umo-dialog__ctx umo-dialog__ctx--fixed"
              style="display: none"
            >
              <div class="umo-dialog__mask"></div>
              <div class="umo-dialog__wrap">
                <div class="umo-dialog__position_fullscreen">
                  <div
                    class="umo-dialog umo-dialog__modal-default umo-dialog__fullscreen"
                  >
                    <div
                      class="umo-dialog__header umo-dialog__header--fullscreen"
                    >
                      <div class="umo-dialog__header-content">
                        <!---->
                        <div data-v-8bef958a="" class="preview-header">
                          <div data-v-8bef958a="" class="title">
                            <svg
                              data-v-8bef958a=""
                              class="icon"
                              aria-hidden="true"
                              width="1em"
                              height="1em"
                            >
                              <use
                                xlink:href="#icon-print"
                                fill="currentColor"
                              ></use></svg
                            ><span data-v-8bef958a="">{{$t('print.preview')}}</span>
                          </div>
                          <div data-v-8bef958a="" class="actions">
                            <button
                              data-v-8bef958a=""
                              class="umo-button umo-button--variant-outline umo-button--theme-default umo-size-s"
                              type="button"
                              href=""
                              tabindex="0"
                            >
                            {{$t('pageOptions.title')}}</button
                            ><button
                              data-v-8bef958a=""
                              class="umo-button umo-button--variant-outline umo-button--theme-default umo-size-s"
                              type="button"
                              href=""
                              tabindex="0"
                            >
                            {{$t('print.settings')}}</button
                            ><!---->
                          </div>
                        </div>
                      </div>
                      <span
                        class="umo-dialog__close umo-dialog__close--fullscreen"
                        ><svg
                          fill="none"
                          viewBox="0 0 24 24"
                          width="1em"
                          height="1em"
                          class="t-icon t-icon-close"
                        >
                          <path
                            fill="currentColor"
                            d="M7.05 5.64L12 10.59l4.95-4.95 1.41 1.41L13.41 12l4.95 4.95-1.41 1.41L12 13.41l-4.95 4.95-1.41-1.41L10.59 12 5.64 7.05l1.41-1.41z"
                          ></path></svg
                      ></span>
                    </div>
                    <div class="umo-dialog__body umo-dialog__body--fullscreen">
                      <div data-v-8bef958a="" class="preview-container">
                        <div data-v-8bef958a="" class="umo-loading__parent">
                          <iframe data-v-8bef958a="" srcdoc=""></iframe
                          ><!---->
                        </div>
                      </div>
                    </div>
                    <div
                      class="umo-dialog__footer umo-dialog__footer--fullscreen"
                    >
                      <div>
                        <button
                          class="umo-button umo-button--variant-base umo-button--theme-default"
                          type="button"
                          href=""
                          tabindex="0"
                        >
                          <span class="umo-button__text"
                            >{{$t('print.closePreview')}}</span
                          ></button
                        ><button
                          class="umo-button umo-button--variant-base umo-button--theme-primary umo-dialog__confirm"
                          type="button"
                          href=""
                          tabindex="0"
                        >
                          <span class="umo-button__text">{{$t('print.text')}}</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!---->
          </div>
          <div class="el-overlay" style="z-index: 2003; display: none">
            <div
              role="dialog"
              aria-modal="true"
              aria-label="关于“”的全部对话"
              aria-describedby="el-id-1541-2"
              class="el-overlay-dialog"
            ></div>
          </div>
          <!--v-if-->
        </main>
      </div>
      <!-- <router-view /> -->
    </div>
  </div>
</template>

<script setup>
import { appuuid as titles , home } from "@/langs/lang.js";
import { getParameters, getAppByUuid } from "@/api/base";
const url = ref("");
const appLang = ref({});
const remard = ref("");
const title = ref("");
const ai_apps_lang = useCookie("ai_apps_lang", {
  domain:
    import.meta.env.VITE_MODE === "development"
      ? "localhost"
      : import.meta.env.VITE_MODE === "test"
      ? ".medon.com.cn"
      : ".medsci.cn",
  maxAge: 30 * 24 * 60 * 60 * 12,
});
import { useI18n } from "vue-i18n";
const { t, locale } = useI18n();
const { data: parameters } = await useAsyncData(
  "parameters",
  async () => {
    let res;
    let appLang;
    try {
      const route = useRoute();
      url.value = import.meta.env.VITE_BASE_URL+"/ai-write/" + route.params.appUuid;
      const userInfo = useCookie("userInfo");
      const header = useRequestHeaders();
      header['accept-language'] = header['accept-language'] ? header['accept-language']:'zh-CN,zh;q=0.9,en;q=0.8'
      ai_apps_lang.value = locale.value;
      // 获取 event
      const event = useRequestEvent();
      appLang = await getAppByUuid(route.params.appUuid, event);
      title.value = `${appLang?.appName}\u002D${titles[ai_apps_lang.value]}`;
      res = await getParameters(
        {
          appId: appLang?.dAppUuid,
          user: userInfo?.userName,
        },
        event
      );
    } catch (error) {
      console.log(error);
    }
    return [res, appLang];
  },
  { server: true }
);
appLang.value = parameters.value[1];
const convertMarkdownToHtml = (markdown) => {
    // 使用正则表达式匹配对应的标题级别并替换为相应的HTML标签
    return markdown
        .replace(/^###### (.*$)/gim, '<h6>\$1</h6>')
        .replace(/^##### (.*$)/gim, '<h5>\$1</h5>')
        .replace(/^#### (.*$)/gim, '<h4>\$1</h4>')
        .replace(/^### (.*$)/gim, '<h3>\$1</h3>')
        .replace(/^## (.*$)/gim, '<h2>\$1</h2>')
        .replace(/^# (.*$)/gim, '<h1>\$1</h1>')
        .replace(/\n{2,}/g, ''); // 替换连续的换行符为 <br> 标签
}
if(parameters.value[1]?.directoryMd){
  remard.value = convertMarkdownToHtml(parameters.value[1]?.directoryMd)
}
useHead({
  title: title.value,
  meta: [
    {
      name: "keywords",
      content: `${home['writeKeywords'][ai_apps_lang.value]},${appLang.value?.appName}`,
    },
    {
      name: "description",
      content: appLang.value?.appDescription,
    },
    { property: "og:type", content: "website" },
    { property: "og:title", content: title.value },
    { property: "og:description", content: appLang.value?.appDescription },
    { property: "og:image", content: appLang.value?.appIcon },
    { name: "twitter:card", content: "summary_large_image" }, // 注意：根据常见用法推断 content 为 'summary_large_image'
    { name: "twitter:title", content: title.value },
    { name: "twitter:description", content: appLang.value?.appDescription },
    { name: "twitter:image", content: appLang.value?.appIcon },
  ],
});
</script>
<style scoped>
iframe {
  width: 100%;
  height: 100vh;
  border: none;
}
#app {
  display: none;
}
</style>
