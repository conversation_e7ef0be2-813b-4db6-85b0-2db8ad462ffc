import { createI18n } from 'vue-i18n'
import { getConfigPage } from "@/api/base";
import { 
    transformArrayToObject, 
    setItemWithTimestamp 
} from "@/common/commonJs";


// i18n.config.js
export default async () => {
    let messages
    const event = process.server ? useRequestEvent() : null;
    // 1. 调用API获取语言配置
    const response = await getConfigPage('zh',event);
        if (response?.list?.length) {
            const cacheKey = 'current_langs_pack';
            const langsStr = JSON.stringify(transformArrayToObject(response.list));
             messages = transformArrayToObject(response.list);
            // setItemWithTimestamp(cacheKey, langsStr);
        }
    
    // 2. 返回VueI18n配置
    return {
      legacy: false, // 使用Composition API
      locale: 'zh', // 默认语言
      fallbackLocale: 'zh',
      messages: messages, // 使用API返回的消息
      // 其他配置...
    }
  }