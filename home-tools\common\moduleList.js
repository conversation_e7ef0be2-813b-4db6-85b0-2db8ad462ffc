import { getAssetsFile } from "@/utils/index";
const data = [
  {
    name: "writingAssistant",
    path: "/writingAssistant",
    type: "医学写作",
    meta: {
      title: "论文",
      dify_app_uuid: "10c03431-1933-4f59-8848-e2e3ccb2c557",
      desc: '医学论文是医学领域的研究者撰写的，旨在探讨疾病病因、诊断、治疗、预防或医学理论、技术进展的学术性文章。它基于科学实验或临床观察，通过严谨的数据分析，提出新发现、新观点或验证已有理论，为医学实践提供科学依据和指导。',
      icon: getAssetsFile("AI写作助手.svg"),
      bg: getAssetsFile("基于AI的写作文本加工.png"),
    },
    component: () => import("@/views/writingAssistant/index.vue"),
  },
  {
    name: "intelligentPolishing",
    path: "/intelligentPolishing",
    type: "医学写作",
    meta: {
      title: "综述",
      dify_app_uuid: "4955c5cc-a366-45d8-8046-aa3044ff77bc",
      desc: "医学综述是对某一医学领域或专题在一定时期内研究成果的汇总和分析，旨在系统回顾研究进展，评价研究质量，探讨存在问题，预测未来方向，为研究者提供全面、深入的信息参考。",
      icon: getAssetsFile("AI智能润色.svg"),
      bg: getAssetsFile("基于AI的写作文本加工.png"),
    },
    component: () => import("@/views/intelligentPolishing/index.vue"),
  },
  {
    name: "reviewer",
    path: "/reviewer",
    type: "医学写作",
    meta: {
      title: "研究方案",
      dify_app_uuid: "d7df5b59-280e-4dda-b0f2-700a12b0b1f9",
      desc: "医学研究方案是科研人员为探索医学问题而设计的详细计划，包括研究目的、假设、方法、样本选择、数据收集与分析步骤、预期结果及伦理考量等，确保研究过程科学、严谨、可重复。",
      icon: getAssetsFile("审稿人回复信.svg"),
      bg: getAssetsFile("基于AI的写作文本加工.png"),
    },
    component: () => import("@/views/reviewer/index.vue"),
  },
    {
    name: "rewrite",
    path: "/rewrite",
    type: "医学写作",
    meta: {
      title: "软文",
      dify_app_uuid: "d1fc1b66-47ce-45cd-9ff9-ee49a2e44881",
      desc: "医学软文是以医疗、健康领域为背景，通过撰写具有吸引力和可读性的文章，间接推广医疗产品、服务或健康理念的一种营销手段。",
      icon: getAssetsFile("AI降重改写.svg"),
      bg: getAssetsFile("基于AI的写作文本加工.png"),
    },
    component: () => import("@/views/rewrite/index.vue"),
  },
  {
    name: "topTitle",
    path: "/topTitle",
    type: "医学写作",
    meta: {
      title: "指南共识",
      dify_app_uuid: "7252420d-c356-4bed-9e06-ce6c3bc01270",
      desc: "指南共识是指在医学领域中，基于系统评价的证据和专家经验，针对特定临床问题制定的指导性建议或推荐意见。",
      icon: getAssetsFile("高分title生成器.svg"),
      bg: getAssetsFile("题材和论文章节的写作语料.png"),
    },
    component: () => import("@/views/topTitle/index.vue"),
  },
  {
    name: "reviewSearch",
    path: "/reviewSearch",
    type: "医学写作",
    meta: {
      title: "病例报告",
      dify_app_uuid: "05544f18-9efb-42e4-ad5d-8ca78feaf56c",
      desc: "病例报告是详细记录并分析个别患者疾病发生、诊断、治疗及转归过程的医学文献，用于探讨罕见病、特殊临床表现或治疗经验。",
      icon: getAssetsFile("综述Review.svg"),
      bg: getAssetsFile("题材和论文章节的写作语料.png"),
    },
    component: () => import("@/views/reviewSearch/index.vue"),
  },
    {
    name: "reviewSearch",
    path: "/reviewSearch",
    type: "医学写作",
    meta: {
      title: "国自然基金写作",
      dify_app_uuid: "cc0c4388-8c02-4f1c-8a32-e0f3e84146c7",
      desc: "“国自然基金写作”是撰写国家自然科学基金申请书的过程，该过程需遵循科学基金的撰写要求和规范，涵盖项目名称、关键词、摘要、立项依据、研究目标、内容、方案及创新性等多个方面的撰写，旨在清晰、准确、有逻辑地展示研究项目的科学性、创新性和可行性，以获得基金资助。",
      icon: getAssetsFile("综述Review.svg"),
      bg: getAssetsFile("基于AI的写作文本加工.png"),
    },
    component: () => import("@/views/reviewSearch/index.vue"),
  },
  //  {
  //   name: "reviewSearch",
  //   path: "/chat",
  //   type: "医学会话",
  //   meta: {
  //     title: "技术开发问答",
  //     dify_app_uuid: "722c31c6-ef98-4315-b639-a600f77af5b7",
  //     desc: "",
  //     icon: getAssetsFile("场景写作.svg"),
  //     bg: getAssetsFile("基于AI的写作文本加工.png"),
  //   },
  //   component: () => import("@/views/reviewSearch/index.vue"),
  // },
       {
     name: "reviewSearch",
     path: "/chat",
     type: "医学会话",
     meta: {
       title: "学术写作问答",
       dify_app_uuid: "c81251e7-9269-413b-95d3-0a4b88ce84b9",
       desc: "",
       icon: getAssetsFile("场景写作.svg"),
       bg: getAssetsFile("基于AI的写作文本加工.png"),
     },
     component: () => import("@/views/reviewSearch/index.vue"),
  },
    {
     name: "reviewSearch",
     path: "/chat",
     type: "医学会话",
     meta: {
       title: "AI产品发布会",
       dify_app_uuid: "5ec1c9f5-a9a3-4697-bb66-71793e8375a6",
       desc: "",
       icon: getAssetsFile("场景写作.svg"),
       bg: getAssetsFile("基于AI的写作文本加工.png"),
     },
     component: () => import("@/views/reviewSearch/index.vue"),
   },
  // {
  //   name: "correction",
  //   path: "/correction",
  //   type: "基于AI的写作文本加工",
  //   meta: {
  //     title: "AI写作语法纠错",
  //     desc: "运用ChatGPT和自研模型的纠错算法，实现学术写作中的语法纠错功能。",
  //     icon: getAssetsFile("语法纠错.svg"),
  //     bg: getAssetsFile("基于AI的写作文本加工.png"),
  //   },
  //   component: () => import("@/views/correction/index.vue"),
  // },
 
  //  {
  //   name: "runSe",
  //   path: "/runSe",
  //   type: "科研工具",
  //   meta: {
  //     title: "智能润色助手",
  //     dify_app_uuid: "ab2b1f22-5aa4-4f66-bbf2-b147a8ad5500",
  //     desc: "",
  //     icon: getAssetsFile("AI智能润色.svg"),
  //     bg: getAssetsFile("基于AI的写作文本加工.png"),
  //   },
  //   component: () => import("@/views/aiBase/index.vue"),
  // },
  // {
  //   name: "reviewSearch",
  //   path: "/reviewSearch",
  //   type: "科研工具",
  //   meta: {
  //     title: "查重修改助手",
  //     dify_app_uuid: "d9307f7d-59e2-42a0-a77d-a95d18c0dd52",
  //     desc: "",
  //     icon: getAssetsFile("AI降重改写.svg"),
  //     bg: getAssetsFile("基于AI的写作文本加工.png"),
  //   },
  //   component: () => import("@/views/reviewSearch/index.vue"),
  // },
  // {
  //   name: "reviewSearch",
  //   path: "/reviewSearch",
  //   type: "科研工具",
  //   meta: {
  //     title: "智能标题助手",
  //     dify_app_uuid: "17f7f079-ea37-4680-8f73-9013f2fdef59",
  //     desc: "",
  //     icon: getAssetsFile("高分title生成器.svg"),
  //     bg: getAssetsFile("基于AI的写作文本加工.png"),
  //   },
  //   component: () => import("@/views/reviewSearch/index.vue"),
  // },
  // {
  //   name: "reviewSearch",
  //   path: "/reviewSearch",
  //   type: "科研工具",
  //   meta: {
  //     title: "场景写作助手",
  //     dify_app_uuid: "17f7f079-ea37-4680-8f73-9013f2fdef59",
  //     desc: "",
  //     icon: getAssetsFile("场景写作.svg"),
  //     bg: getAssetsFile("基于AI的写作文本加工.png"),
  //   },
  //   component: () => import("@/views/reviewSearch/index.vue"),
  // },
  // {
  //   name: "reportSearch",
  //   path: "/reportSearch",
  //   type: "题材和论文章节的写作语料",
  //   meta: {
  //     title: "个案/病例报道写作语料库",
  //     desc: "实时收录全网Case report文献，提取并归类语料，供综述写作时参考。",
  //     icon: getAssetsFile("个案报道.svg"),
  //     bg: getAssetsFile("题材和论文章节的写作语料.png"),
  //   },
  //   component: () => import("@/views/reportSearch/index.vue"),
  // },
  // {
  //   name: "imgSearch",
  //   path: "/imgSearch",
  //   type: "题材和论文章节的写作语料",
  //   meta: {
  //     title: "图例写作语料库",
  //     desc: "实时收录全网SCI文献的图例，请按您所用图片的关键词进行查找，供综述写作时参考。",
  //     icon: getAssetsFile("图例写作.svg"),
  //     bg: getAssetsFile("题材和论文章节的写作语料.png"),
  //   },
  //   component: () => import("@/views/imgSearch/index.vue"),
  // },
  // {
  //   name: "acaSearch",
  //   path: "/acaSearch",
  //   type: "题材和论文章节的写作语料",
  //   meta: {
  //     title: "场景写作助手",
  //     desc: "收录写作中会遇到的场景及对应表述方式，场景顺序也是写作的逻辑顺序。帮助使用者实现学术论文有格式化的特点，推荐AI写作助手协同使用，进一步提高写作效率。",
  //     icon: getAssetsFile("场景写作.svg"),
  //     bg: getAssetsFile("题材和论文章节的写作语料.png"),
  //   },
  //   component: () => import("@/views/acaSearch/index.vue"),
  // },
  // {
  //   name: "medReading",
  //   path: "/medReading",
  //   type: "题材和论文章节的写作语料",
  //   meta: {
  //     title: "Pubmed中文版",
  //     url: "https://www.medreading.cn/query?source=hr&token=&tk=",
  //     desc: "Medreading是汉化版的Pubmed，增加了期刊影响因子、分区筛选，和丰富的文献分析，全文求助下载功能。",
  //     icon: getAssetsFile("中文版Pubmed.svg"),
  //     bg: getAssetsFile("题材和论文章节的写作语料.png"),
  //   },
  //   component: () => import("@/views/outSide/index.vue"),
  // },
  // {
  //   name: "ideaIgnite",
  //   path: "/ideaIgnite",
  //   type: "国自然查询分析以及申请书写作",
  //   meta: {
  //     title: "Idealgnite高分选题",
  //     desc: "阅读高质量的文献，获得课题思路。根据最新发表的CNS文献，或者在上传的文献基础上，总结潜在的、新的课题思路，同时查看每个思路在已有文献的发表情况。",
  //     icon: getAssetsFile("高分选题.svg"),
  //     bg: getAssetsFile("国自然查询分析以及申请书写作.png"),
  //   },
  //   component: () => import("@/views/ideaIgnite/index.vue")
  // },
  // {
  //   name: "bidAnalysis",
  //   path: "/bidAnalysis",
  //   type: "国自然查询分析以及申请书写作",
  //   meta: {
  //     title: "国自然查询分析",
  //     desc: "收录近超30年（1989-2023）国家自然科学基金立项标书关键信息，结合多重整合分析，数据图形化展示，历年研究热点和立项标书特点一目了然。帮助使用者分析国自然历年数据，把握研究热点。",
  //     icon: getAssetsFile("国自然数分.svg"),
  //     bg: getAssetsFile("国自然查询分析以及申请书写作.png"),
  //   },
  //   component: () => import("@/views/bidAnalysis/index.vue")
  // },
  // {
  //   name: "cellularAnimals",
  //   path: "/cellularAnimals",
  //   type: "国自然查询分析以及申请书写作",
  //   meta: {
  //     title: "细胞系/动物模型查询",
  //     desc: "应用匹配某个疾病或研究内容，罗列文献中已有报道的细胞系和动物模型，解释说明，帮助快速理解。帮助使用者在基础实验中研究某一个疾病对应的细胞系和动物模型，分别代表体外和体内实验来论证课题结论。",
  //     icon: getAssetsFile("细胞动物模型查询.svg"),
  //     bg: getAssetsFile("国自然查询分析以及申请书写作.png"),
  //   },
  //   component: () => import("@/views/cellularAnimals/index.vue")
  // },
  // {
  //   name: "expDesign",
  //   path: "/expDesign",
  //   type: "国自然查询分析以及申请书写作",
  //   meta: {
  //     title: "实验方案设计",
  //     desc: "应用匹配某个疾病或研究内容，自动罗列文献中对应的、已有报道的细胞系和动物模型，同时给于解释说明，帮助快速理解。",
  //     icon: getAssetsFile("实验方案设计.svg"),
  //     bg: getAssetsFile("国自然查询分析以及申请书写作.png"),
  //   },
  //   component: () => import("@/views/expDesign/index.vue")
  // },
  // {
  //   name: "proAssistant",
  //   path: "/proAssistant",
  //   type: "国自然查询分析以及申请书写作",
  //   meta: {
  //     title: "课题思路设计",
  //     desc: "应用关键词，从海量文献中检索与之相关的gene、pathway，按年度分析可尽览其“研究演变”。实现基础科研的课题设计三要素：某个基因通过某信号通路产生某个表型/功能。",
  //     icon: getAssetsFile("课题思路助手.svg"),
  //     bg: getAssetsFile("国自然查询分析以及申请书写作.png"),
  //   },
  //   component: () => import("@/views/proAssistant/index.vue")
  // }
];
export default data;
