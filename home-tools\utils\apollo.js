import { ApolloClient, createHttpLink, InMemoryCache } from '@apollo/client/core'
import { setContext } from "@apollo/client/link/context";
let uri = import.meta.env.VITE_GRAPHQL_API_URL
if (import.meta.env.VITE_MODE === 'dev') {
  uri ='/graphql'
 }
  const httpLink = createHttpLink({
    uri: uri
  })
  const authLink = setContext((_, { headers }) => {
  return {
    headers: {
      ...headers,
      authorization: `Bearer ${getToken('hasuraToken')}`,
      'x-Hasura-Role': 'tenant_admin',
    },
  };
});
const cache = new InMemoryCache()

const apolloClient = new ApolloClient({
    link: authLink.concat(httpLink),
    // link: httpLink,
    cache,
})
export default apolloClient;