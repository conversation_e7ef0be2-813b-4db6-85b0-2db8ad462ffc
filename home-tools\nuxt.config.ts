export default defineNuxtConfig({
  modules: ['@nuxtjs/tailwindcss','@nuxtjs/i18n','@nuxtjs/sitemap'],
  site: {
    url: 'https://ai.medon.com.cn',
    name: '梅斯小智',
  },
  devtools: { 
    enabled: false  // 完全关闭DevTools调试面板
  },
  sitemap: {
    debug: false,
    // 保持多语言分割
    autoI18n: false,
    exclude: [
      '*/tool/components/*',
      '/tool/components/*',
      '*/login',
      '*/login',
      '/login',
      '*/sign-up',
      '/sign-up',
      '*/tool/privacy-policy',
      '*/tool/destroy',
      '/tool/privacy-policy',
      '/tool/destroy'
    ],
    // 禁用动态生成
    urls: async () => {
      const response = await fetch('https://ai.medon.com.cn/dev-api/ai-base/index/getSiteMap')
      const products = await response.json();
      const enums = {
        '写作':'/write',
        '问答':'/chat',
        '工具':'/tool',
      }
      const languages = {
        "中文":"",
        "葡萄牙语":"pt",
        "英文":"en",
        "阿拉伯语":"ar",
        "繁体中文":"tw",
        "印尼语":"id",
        "日语":"ja",
        "韩语":"ko",
        "越南语":"vi",
        "马来语":"ms",
        "西班牙语":"es"
      }
      const   result =
        [{
          loc: '/',
          lastmod: new Date().toISOString(),
          priority: 1,
        },
        {
          loc: '/pt',
          lastmod: new Date().toISOString(),
          priority: 0.8,
        },{
          loc: '/en',
          lastmod: new Date().toISOString(),
          priority: 0.8,
        },{
          loc: '/ar',
          lastmod: new Date().toISOString(),
          priority: 0.8,
        },{
          loc: '/tw',
          lastmod: new Date().toISOString(),
          priority: 0.8,
        },{
          loc: '/id',
          lastmod: new Date().toISOString(),
          priority: 0.8,
        },{
          loc: '/ja',
          lastmod: new Date().toISOString(),
          priority: 0.8,
        },{
          loc: '/ko',
          lastmod: new Date().toISOString(),
          priority: 0.8,
        },{
          loc: '/vi',
          lastmod: new Date().toISOString(),
          priority: 0.8,
        },{
          loc: '/ms',
          lastmod: new Date().toISOString(),
          priority: 0.8,
        },{
          loc: '/es',
          lastmod: new Date().toISOString(),
          priority: 0.8,
        },{
          loc: '/idoc',
          lastmod: new Date().toISOString(),
          priority: 0.8,
        }]
        result.push( ...(products.data || []).filter(
        (product: { appType: string; appNameEn: string,creationTime:string,appLang:string }) => enums[product.appType as keyof typeof enums] && product.appNameEn
      ).map((product: { appType: string; appNameEn: string,creationTime:string,appLang:string }) => ({
        loc: `/${languages[product.appLang as keyof typeof languages]}${enums[product.appType as keyof typeof enums]}/${product.appNameEn}`,
        lastmod: new Date(product.creationTime).toISOString() || new Date().toISOString(),
        priority: languages[product.appLang as keyof typeof languages] || enums[product.appType as keyof typeof enums] ? 0.8 : 1,
      })))
      
      return result
    }
  },
  i18n: {
    strategy: 'prefix_except_default',
    defaultLocale: 'zh',
    locales: [
      { code: 'zh', iso: 'zh-CN', name: '简体中文' },
      { code: 'en', iso: 'en-US', name: 'English' },
      { code: 'tw', iso: 'zh-TW', name: '繁體中文' },
      { code: 'ar', iso: 'ar-SA', name: 'العربية' },
      { code: 'es', iso: 'es-ES', name: 'Español' },
      { code: 'id', iso: 'id-ID', name: 'Bahasa Indonesia' },
      { code: 'ja', iso: 'ja-JP', name: '日本語' },
      { code: 'ko', iso: 'ko-KR', name: '한국어' },
      { code: 'pt', iso: 'pt-BR', name: 'Português' },
      { code: 'vi', iso: 'vi-VN', name: 'Tiếng Việt' },
      { code: 'ms', iso: 'ms-MY', name: 'Bahasa Melayu' },
    ],
    detectBrowserLanguage:false,
    vueI18n: '~/i18n.config.js',
  },
  // devServer: {
  //   host: '0.0.0.0',
  // },
  vite: {
    server: {
      fs: {
        // 允许访问项目目录
        allow: ['D:/项目/nuxt-demo']
      }
    },
    esbuild: {
      pure:  ['console.log']   // 移除 log
    },
    build: {
      rollupOptions: {
        external: ['@wangeditor/editor-for-vue', '@wangeditor/editor/dist/css/style.css', 'marked', 'marked-highlight']
      }
    }
  },
  vue: {
    compilerOptions: {
      isCustomElement: (tag) => tag === 'v-md-editor'
    }
  },

  postcss: {
    plugins: {
      tailwindcss: {},
      autoprefixer: {}
    }
  },

  app: {
    head: {
      meta: [
        { name: 'viewport', content: 'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no' },
        { name: 'baidu-site-verification', content: 'codeva-vQmNFN9kx1' }
      ],
      // 引入 CSS 资源
      link: [
        { rel: 'icon', type: 'image/x-icon', href: 'https://static.medsci.cn/product/medsci-site/portal/favicon.ico' },
        { rel: 'stylesheet', href: 'https://static.medsci.cn/ai-write/katex.min.css' , body: true },
        { rel: 'stylesheet', href: 'https://static.medsci.cn/product/medsci-site/portal/css/reset.min.css' },
        { rel: 'stylesheet', href: 'https://static.medsci.cn/product/medsci-site/portal/css/owl.carousel.css' },
        { rel: 'stylesheet', href: 'https://static.medsci.cn/product/medsci-site/portal/css/main-stylesheet.min.css' },
        { rel: 'stylesheet', href: 'https://static.medsci.cn/product/medsci-site/portal/css/shortcodes.min.css' },
        { rel: 'stylesheet', href: 'https://static.medsci.cn/product/medsci-site/portal/css/animate.css' },
        { rel: 'stylesheet', href: 'https://static.medsci.cn/product/medsci-site/portal/css/responsive.min.css' },
        { rel: 'stylesheet', href: 'https://img.medsci.cn/web/css/login.min.css?date=1615450612522' },
        { rel: 'stylesheet', href: 'https://img.medsci.cn/web/css/iconfont.css' }
      ],
      // 引入 JS 资源
      script:[
        { src: 'https://static.medsci.cn/product/medsci-site/portal/js/jquery-latest.min.js' },
        { src: 'https://static.medsci.cn/ai-write/katex.min.js', body: true },
        { src: 'https://static.medsci.cn/ai-write/mermaid.min.js', body: true },
        { src: 'https://static.medsci.cn/product/medsci-site/iframe-test-v1.js', body: true},
        { src: 'https://static.medsci.cn/product/medsci-site/portal/js/owl.carousel.min.js', defer: true },
        { src: 'https://static.medsci.cn/ai-write/analytics.js', body: true , tagPosition: 'bodyClose'}
      ],
    }
  },
  plugins: [
    { src: '~/plugins/vant.js', ssr: true },
    // { src: '~/plugins/sitemap.ts', ssr: true },
    { src: '~/plugins/vue-qr.js', ssr: false },
    // { src: '~/plugins/i18n.js', mode: 'all' },
    { src:'~/plugins/element-plus.js'},
    { src:'~/plugins/v-md-preview.client.js',ssr: false},
    // { src:'~/plugins/router.js'},
    // { src:'~/plugins/vconsole.client.js', mode: 'client'}
  ],

  css: [
    'vant/lib/index.css', // 全局引入样式
    'element-plus/dist/index.css',
    '~/assets/css/tailwind.css',
  ],

  nitro: {
    devProxy: {
      '/dev-api': {
        target: 'https://ai.medon.com.cn/dev-api', // 这里替换为实际的后端API地址
        changeOrigin: true,
        prependPath: true
      }
    },
    output: {
      dir: '../ai-write-public/apps/home-tools/'
    }
  },

  compatibilityDate: '2025-04-09'
})