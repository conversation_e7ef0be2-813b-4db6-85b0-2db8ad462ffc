import { marked } from 'marked';

const renderer = new marked.Renderer();
const originalLinkRenderer = renderer.link;

renderer.link = function (href, title, text) {
  const html = originalLinkRenderer.call(this, href, title, text);
  return html.replace(/^<a /, '<a target="_blank" rel="noopener noreferrer" ');
};

// 使用自定义渲染器
const msMarked = (markdown) => {
  return marked(markdown, { renderer });
};

export default msMarked;
