// 滚动条样式
.scrollbar::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

.scrollbar::-webkit-scrollbar-thumb {
  //滑块部分
  border-radius: 5px;
  background-color: #e2e2e2;
}

.scrollbar::-webkit-scrollbar-track {
  //轨道部分
  background: none;
}

// 文字超过一行用省略号代替
.textOverflowOne {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
}

// 文字超过二行用省略号代替
.textOverflowTwo {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}
// 文字超过四行用省略号代替
.textOverflowFour {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  line-clamp: 4;
  -webkit-box-orient: vertical;
}

// 过渡
.transition {
  transition: all 0.5s linear;
  -webkit-transition: all 0.5s linear;
  -moz-transition: all 0.5s linear;
  -o-transition: all 0.5s linear;
  -ms-transition: all 0.5s linear;
}

// 手势鼠标
.cursor_pointer {
  cursor: pointer;
}

// 禁止换行
.text_nowrap {
  white-space: nowrap;
}

// 白底内容
.unit_container {
  background-color: #fff;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  padding: 20px;
  padding-top: 0;
}

// 内容区域背景
.container_back {
  min-height: 100%;
  background-color: #fff;
  border-radius: 4px;
}
