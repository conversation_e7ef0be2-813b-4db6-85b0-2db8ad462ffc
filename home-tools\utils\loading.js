import { ElLoading } from "element-plus";
import {getItemWithTimestampCheck, setItemWithTimestamp, transformArrayToObject} from "@/common/commonJs"
import storage from "@/utils/storage";
import { getConfigPage } from "@/api/base";

// 调用接口覆盖语言包数据
const refreshLanguagePack = async () => {
  if (typeof window !== 'undefined') {
    try {
      const response = await getConfigPage();
      if (response?.list?.length) {
        const langsStr = JSON.stringify(transformArrayToObject(response.list));
        setItemWithTimestamp('current_langs_pack', langsStr);
        console.log('语言包数据已更新');
        return true;
      }
    } catch (error) {
      console.error('更新语言包失败:', error);
      return false;
    }
  }
  return false;
};
const lang = storage.get('ai_apps_lang') || 'en'
const langes = getItemWithTimestampCheck('current_langs_pack',7) ? JSON.parse(getItemWithTimestampCheck('current_langs_pack',7))[lang]?.tool : '数据加载中'
const defaultOption = {
  lock: true,
  text: langes['loadingData'] ?? '数据加载中',
  background: "rgba(225, 225, 225, 0.3)",
};
// loading实例
let loading;

export const $loading = {
  // 打开loading
  showLoading: (opt) => {
    loading = ElLoading.service(opt || defaultOption);
  },
  // 关闭loading
  hideLoading: () => {
    if (loading) {
      loading.close();
      loading = null;
    }
  },
  // 刷新语言包数据
  refreshLanguagePack: refreshLanguagePack
};

// 自动执行一次语言包刷新
if (typeof window !== 'undefined') {
  // 延迟执行，确保模块初始化完成
  setTimeout(() => {
    refreshLanguagePack();
  }, 100);
}
