<template>
  <div class="flex relative h-full overflow-auto">
    <!-- 内容 -->
    <div class="w-[58%]">
      <!--  -->
      <div class="flex items-center my-4">
        <div
          class="px-4 py-1 rounded mr-4 font-bold cursor-pointer"
          :class="type == 1 ? 'bg-[#409eff] text-white' : 'bg-gray-200 text-[#606266]'"
          @click="onType(1)"
        >
          审稿意见
        </div>
        <div
          class="px-4 py-1 rounded mr-4 font-bold cursor-pointer"
          :class="type == 2 ? 'bg-[#409eff] text-white' : 'bg-gray-200 text-[#606266]'"
          @click="onType(2)"
        >
          回复内容
        </div>
      </div>
      <!-- 查询 -->
      <div class="flex items-center bg-[#f7f8fa] p-2 rounded-md h-[52px] mb-2">
        <el-input
          class="h-full !text-[24px]"
          v-model="queryTitle"
          :placeholder="placeholder"
          clearable
        />
        <el-button type="primary" @click="getResult(1)">查 询</el-button>
      </div>
      <!-- 工具栏 -->
      <div class="flex items-center my-4">
        <!-- 翻译开关 -->
        <div class="flex items-center mr-4">
          <span class="mr-2" :class="translate ? 'text-[#409eff]' : ''"
            >翻译</span
          >
          <el-switch v-model="translate" />
        </div>
      </div>
      <!-- 查询结果 -->
      <div>
        <div
          class="flex mb-8"
          v-for="(item, index) in result.content"
          :key="index"
        >
          <div class="mr-2">{{ index + 1 }}.</div>
          <div>
            <div
              class="text-[18px] text-[#5e5e5e] cursor-pointer html-value"
              v-html="item.text"
              @click="handleClick(index + 1, item.text)"
            ></div>
            <!-- 翻译 -->
            <div
              class="text-[16px] text-[#0a76f5] mt-4"
              v-html="item.translateText"
              v-if="translate"
            ></div>
          </div>
        </div>
      </div>
      <!-- 分页 -->
      <Pagination
        v-if="result && result.eleTotal"
        class="pb-10"
        :total="result.eleTotal"
        v-model:page="paging.pageNo"
        v-model:limit="paging.pageSize"
        @pagination="getResult"
      />
    </div>
    <!-- 编辑器 -->
    <div class="flex-1 ml-4 box-right">
      <Editor
        class="h-[380px] fixed z-99"
        :style="{width: editorWidth + 'px'}"
        v-model="htmlvalue"
        @clear="onClear"
        :key="editorKey"
      />
    </div>
  </div>
</template>

<script setup>
import { medsciAi } from "@/api/index";
import { shuffleArray } from "@/utils/index"
const queryTitle = ref("supplement data needed");
const htmlvalue = ref("");
const editorKey = ref(1);
const translate = ref(true);
const result = ref({});
const placeholder = ref("请输入审稿人的意见关键词或短句（中/英）");
const type = ref(1);
const paging = reactive({
  pageNo: 1,
  pageSize: 20,
});
const editorWidth = ref(344)
// 
const onType = (val) => {
  queryTitle.value = ''
  result.value = {}
  type.value = val;
  placeholder.value = val == 1 ? "请输入审稿人的意见关键词或短句（中/英）" : "请输入回复关键词或短句（中/英）";
}
// 获取数据
const getResult = (type) => {
  if (!queryTitle.value) return ElMessage.warning("请输入文本")
  if (type == 1) paging.pageNo = 1
  medsciAi('sentence', {
    searchKey: queryTitle.value,
    page: paging.pageNo - 1,
    size: paging.pageSize,
  }).then(res => {
    if (!res || !res.data) return
    result.value = res.data
    result.value.content = shuffleArray(result.value.content)
  })
};
// 点击粘贴
const handleClick = (index, text) => {
  let str = `${index}.${text}`.replace(/\n/g, "");
  htmlvalue.value += `<p>${str}</p>`;
};
// 编辑器清空时
const onClear = () => {
  editorKey.value++;
};

onMounted(() => {
  getResult()
  editorWidth.value = document.querySelector(".box-right").offsetWidth
  window.onresize = () => {
    editorWidth.value = document.querySelector(".box-right").offsetWidth
  }
})

</script>

<style lang="scss" scoped>
:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color))
    inset;
  background: #f7f8fa;
  cursor: default;
  .el-input__inner {
    cursor: default !important;
    color: #000;
    font-weight: 400;
  }
}
.html-value:hover {
  color: rgb(114, 205, 254);
  :deep(em) {
    color: rgb(114, 205, 254) !important;
  }
}
</style>
