<template>
  <div class="editor border border-solid border-gray-400 h-full rounded">
    <Toolbar
      class="border-b border-t-0 border-l-0 border-r-0 border-solid border-gray-400 mx-4"
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
      :mode="mode"
    />
    <Editor
      style="height: calc(100% - 41px - 32px)"
      v-model="valueHtml"
      :defaultConfig="editorConfig"
      :mode="mode"
      @onChange="onChange"
      @onCreated="onCreated"
    />
    <div
      class="text-[12px] h-[32px] mx-4 border-t border-solid border-gray-400 border-b-0 border-l-0 border-r-0 flex items-center justify-end"
    >
      <el-icon size="14" class="mr-2 cursor-pointer" title="清空" @click="onDelete">
        <Delete/>
      </el-icon>
      <span>{{ valueHtml ? valueHtml.replace(/<[^>]+>/g,"").length : 0 }}字</span>
    </div>
  </div>
</template>

<script setup>
import "@wangeditor/editor/dist/css/style.css";
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import { Delete } from "@element-plus/icons-vue";
const props = defineProps({
  modelValue: String,
  placeholder: {
    type: String,
    default: "写作区 (鼠标单击搜索结果，即可黏贴至此处)"
  }
});
// https://www.wangeditor.com/v5/getting-started.html
// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef(null);
const valueHtml = ref("");
// 工具栏模式
const mode = ref("simple");
// 工具栏配置项
const toolbarConfig = {
  toolbarKeys: [
    "undo", // 撤销
    "redo", // 重做
    "bold", // 粗体
    "italic", // 斜体
    "underline", // 下划线
    "through", // 删除线
    "sub", // 下标
    "sup", // 上标
  ],
  //排除不需要的菜单
  // excludeKeys: [
  //   "insertLink",
  //   "viewImageLink",
  //   "insertVideo",
  //   "emotion",
  //   "fullScreen",
  //   "codeBlock",
  //   "todo",
  //   "uploadImage",
  //   "groupImage",
  //   "blockquote",
  //   "insertTable",
  //   "group-image",
  // ]
};
// 编辑器配置
const editorConfig = ref({
  placeholder: props.placeholder,
  readOnly: false,
  autoFocus: false,
  scroll: true,
});
const emits = defineEmits(["update:modelValue", 'clear']);
watch(
  () => props.modelValue,
  (val) => {
    valueHtml.value = val;
  },
  {
    immediate: true,
  }
);

// watch(
//   () => props.placeholder,
//   (val) => {
//     editorConfig.value.placeholder = val;
//   },
//   {
//     immediate: true,
//   }
// )

const onCreated = (editor) => {
  editorRef.value = editor; //记录编辑器实例，重要！
};
const onChange = (e) => {
  valueHtml.value = e.getHtml();
  emits("update:modelValue", valueHtml.value);
};
const onDelete = () => {
  valueHtml.value = "";
  emits("update:modelValue", valueHtml.value);
  emits('clear')
}
</script>

<style lang="scss" scoped>
.editor {
  font-size: 18px;
  font-family: "Times New Roman";
  color: #5e5e5e;
}
:deep(em) {
  color: red !important;
}
</style>
