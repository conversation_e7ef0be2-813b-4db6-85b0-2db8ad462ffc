# MessageAttachments 组件

一个用于渲染消息附件的 Vue 组件，支持图片、视频、音频和文档等多种文件类型。

## 功能特性

### 🖼️ 图片附件
- 缩略图预览
- 点击放大查看
- 支持常见图片格式（JPG、PNG、GIF、WebP等）
- 错误处理和占位图

### 🎬 视频附件
- 内嵌视频播放器
- 支持常见视频格式（MP4、MOV等）
- 预加载元数据
- 播放控制

### 🎵 音频附件
- 内嵌音频播放器
- 支持常见音频格式（MP3、WAV等）
- 播放控制

### 📄 文档附件
- 文件图标显示
- 点击下载功能
- 文件大小显示
- 支持各种文档格式

## 使用方法

### 基础用法

```vue
<template>
  <MessageAttachments :attachments="messageFiles" />
</template>

<script setup>
import MessageAttachments from '~/components/MessageAttachments/index.vue'

const messageFiles = [
  {
    filename: 'example.jpg',
    type: 'image',
    url: 'https://example.com/image.jpg',
    size: 1024000
  },
  {
    filename: 'document.pdf',
    type: 'document',
    url: 'https://example.com/document.pdf',
    size: 2048000
  }
]
</script>
```

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| attachments | MessageFile[] | [] | 附件数组 |

### MessageFile 接口

```typescript
interface MessageFile {
  filename: string    // 文件名
  type: 'image' | 'video' | 'audio' | 'document'  // 文件类型
  url: string        // 文件URL
  size: number       // 文件大小（字节）
}
```

## 样式定制

组件使用 Tailwind CSS 类名，可以通过以下方式定制样式：

### 1. 覆盖组件样式
```css
/* 自定义附件容器样式 */
.message-attachments {
  /* 你的自定义样式 */
}

/* 自定义图片样式 */
.attachment-image {
  /* 你的自定义样式 */
}
```

### 2. 响应式设计
组件内置响应式支持：
- 桌面端：网格布局，每行多个附件
- 移动端：单列布局，适配小屏幕

## 功能详解

### 图片预览
- 点击图片可以全屏预览
- 支持键盘 ESC 键关闭
- 显示文件名和大小信息

### 文件下载
- 文档类型附件支持点击下载
- 自动处理下载链接
- 在新标签页中打开

### 错误处理
- 图片加载失败时显示占位图
- 视频/音频加载失败时显示错误信息
- 优雅降级处理

## 浏览器兼容性

- 现代浏览器（Chrome、Firefox、Safari、Edge）
- 移动端浏览器
- 支持 HTML5 视频/音频标签

## 注意事项

1. **文件URL**: 确保附件URL可访问且支持跨域
2. **文件大小**: 大文件可能影响加载性能
3. **安全性**: 注意文件来源的安全性
4. **格式支持**: 不同浏览器对媒体格式支持可能不同

## 示例数据

```javascript
const exampleAttachments = [
  {
    filename: '医学报告.pdf',
    type: 'document',
    url: 'https://example.com/report.pdf',
    size: 1024000
  },
  {
    filename: 'X光片.jpg',
    type: 'image',
    url: 'https://example.com/xray.jpg',
    size: 512000
  },
  {
    filename: '心电图视频.mp4',
    type: 'video',
    url: 'https://example.com/ecg.mp4',
    size: 5120000
  },
  {
    filename: '心音录音.mp3',
    type: 'audio',
    url: 'https://example.com/heartbeat.mp3',
    size: 256000
  }
]
```

## 更新日志

### v1.0.0
- 初始版本
- 支持图片、视频、音频、文档四种类型
- 图片预览功能
- 文件下载功能
- 响应式设计
