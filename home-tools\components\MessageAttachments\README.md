# MessageAttachments 组件

一个用于展示消息附件信息的 Vue 组件，以简洁的列表形式显示附件的基本信息。

## 功能特性

### 📋 简洁展示
- 显示文件名、类型、大小
- 文件类型图标
- 清晰的视觉层次

### 🔗 新窗口打开
- 点击附件在新窗口中打开
- 支持所有文件类型
- 简单直接的交互方式

### 📱 响应式设计
- 适配移动端和桌面端
- 自动调整布局和字体大小

### 🎯 支持的文件类型
- **图片**：JPG、PNG、GIF、WebP、SVG等
- **视频**：MP4、MOV、MPEG等
- **音频**：MP3、WAV、M4A等
- **文档**：PDF、DOC、PPT、XLS、TXT等

## 使用方法

### 基础用法

```vue
<template>
  <MessageAttachments :attachments="messageFiles" />
</template>

<script setup>
import MessageAttachments from '~/components/MessageAttachments/index.vue'

const messageFiles = [
  {
    filename: 'medical_report.pdf',
    type: 'document',
    url: 'https://example.com/report.pdf',
    size: 1024000
  },
  {
    filename: 'xray_image.jpg',
    type: 'image',
    url: 'https://example.com/xray.jpg',
    size: 512000
  }
]
</script>
```

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| attachments | MessageFile[] | [] | 附件数组 |

### MessageFile 接口

```typescript
interface MessageFile {
  filename: string    // 文件名
  type: 'image' | 'video' | 'audio' | 'document'  // 文件类型
  url: string        // 文件URL
  size: number       // 文件大小（字节）
}
```

## 样式定制

组件使用 Tailwind CSS 类名，可以通过以下方式定制样式：

### 1. 覆盖组件样式
```css
/* 自定义附件容器样式 */
.message-attachments {
  /* 你的自定义样式 */
}

/* 自定义附件项样式 */
.attachment-item {
  /* 你的自定义样式 */
}
```

### 2. 响应式设计
组件内置响应式支持：
- 桌面端：完整的文件信息显示
- 移动端：紧凑布局，适配小屏幕

## 功能详解

### 文件信息展示
- 文件名（自动截断过长名称）
- 文件类型标签（图片、视频、音频、文档）
- 文件大小（自动格式化）
- 文件类型图标

### 新窗口打开
- 点击任意附件项在新窗口中打开
- 支持所有文件类型
- 保持原始文件URL

## 浏览器兼容性

- 现代浏览器（Chrome、Firefox、Safari、Edge）
- 移动端浏览器
- 支持 HTML5 视频/音频标签

## 注意事项

1. **文件URL**: 确保附件URL可访问且支持跨域
2. **文件大小**: 大文件可能影响加载性能
3. **安全性**: 注意文件来源的安全性
4. **格式支持**: 不同浏览器对媒体格式支持可能不同

## 示例数据

```javascript
const exampleAttachments = [
  {
    filename: '医学报告.pdf',
    type: 'document',
    url: 'https://example.com/report.pdf',
    size: 1024000
  },
  {
    filename: 'X光片.jpg',
    type: 'image',
    url: 'https://example.com/xray.jpg',
    size: 512000
  },
  {
    filename: '心电图视频.mp4',
    type: 'video',
    url: 'https://example.com/ecg.mp4',
    size: 5120000
  },
  {
    filename: '心音录音.mp3',
    type: 'audio',
    url: 'https://example.com/heartbeat.mp3',
    size: 256000
  }
]
```

## 更新日志

### v1.0.0
- 初始版本
- 支持图片、视频、音频、文档四种类型
- 图片预览功能
- 文件下载功能
- 响应式设计
