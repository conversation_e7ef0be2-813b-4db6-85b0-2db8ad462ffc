
// @ts-nocheck


export const localeCodes =  [
  "zh",
  "en",
  "tw",
  "ar",
  "es",
  "id",
  "ja",
  "ko",
  "pt",
  "vi",
  "ms"
]

export const localeLoaders = {
  zh: [],
  en: [],
  tw: [],
  ar: [],
  es: [],
  id: [],
  ja: [],
  ko: [],
  pt: [],
  vi: [],
  ms: []
}

export const vueI18nConfigs = [
  () => import("#nuxt-i18n/f6e5bc61" /* webpackChunkName: "config_i18n_46config_46js_f6e5bc61" */)
]

export const nuxtI18nOptions = {
  restructureDir: "i18n",
  experimental: {
    localeDetector: "",
    switchLocalePathLinkSSR: false,
    autoImportTranslationFunctions: false,
    typedPages: true,
    typedOptionsAndMessages: false,
    generatedLocaleFilePathFormat: "absolute",
    alternateLinkCanonicalQueries: false,
    hmr: true
  },
  bundle: {
    compositionOnly: true,
    runtimeOnly: false,
    fullInstall: true,
    dropMessageCompiler: false,
    optimizeTranslationDirective: true
  },
  compilation: {
    strictMessage: true,
    escapeHtml: false
  },
  customBlocks: {
    defaultSFCLang: "json",
    globalSFCScope: false
  },
  locales: [
    {
      code: "zh",
      iso: "zh-CN",
      name: "简体中文",
      files: []
    },
    {
      code: "en",
      iso: "en-US",
      name: "English",
      files: []
    },
    {
      code: "tw",
      iso: "zh-TW",
      name: "繁體中文",
      files: []
    },
    {
      code: "ar",
      iso: "ar-SA",
      name: "العربية",
      files: []
    },
    {
      code: "es",
      iso: "es-ES",
      name: "Español",
      files: []
    },
    {
      code: "id",
      iso: "id-ID",
      name: "Bahasa Indonesia",
      files: []
    },
    {
      code: "ja",
      iso: "ja-JP",
      name: "日本語",
      files: []
    },
    {
      code: "ko",
      iso: "ko-KR",
      name: "한국어",
      files: []
    },
    {
      code: "pt",
      iso: "pt-BR",
      name: "Português",
      files: []
    },
    {
      code: "vi",
      iso: "vi-VN",
      name: "Tiếng Việt",
      files: []
    },
    {
      code: "ms",
      iso: "ms-MY",
      name: "Bahasa Melayu",
      files: []
    }
  ],
  defaultLocale: "zh",
  defaultDirection: "ltr",
  routesNameSeparator: "___",
  trailingSlash: false,
  defaultLocaleRouteNameSuffix: "default",
  strategy: "prefix_except_default",
  lazy: false,
  langDir: "locales",
  rootRedirect: undefined,
  detectBrowserLanguage: false,
  differentDomains: false,
  baseUrl: "",
  customRoutes: "page",
  pages: {},
  skipSettingLocaleOnNavigate: false,
  types: "composition",
  debug: false,
  parallelPlugin: false,
  multiDomainLocales: false,
  i18nModules: []
}

export const normalizedLocales = [
  {
    code: "zh",
    iso: "zh-CN",
    name: "简体中文",
    files: []
  },
  {
    code: "en",
    iso: "en-US",
    name: "English",
    files: []
  },
  {
    code: "tw",
    iso: "zh-TW",
    name: "繁體中文",
    files: []
  },
  {
    code: "ar",
    iso: "ar-SA",
    name: "العربية",
    files: []
  },
  {
    code: "es",
    iso: "es-ES",
    name: "Español",
    files: []
  },
  {
    code: "id",
    iso: "id-ID",
    name: "Bahasa Indonesia",
    files: []
  },
  {
    code: "ja",
    iso: "ja-JP",
    name: "日本語",
    files: []
  },
  {
    code: "ko",
    iso: "ko-KR",
    name: "한국어",
    files: []
  },
  {
    code: "pt",
    iso: "pt-BR",
    name: "Português",
    files: []
  },
  {
    code: "vi",
    iso: "vi-VN",
    name: "Tiếng Việt",
    files: []
  },
  {
    code: "ms",
    iso: "ms-MY",
    name: "Bahasa Melayu",
    files: []
  }
]

export const NUXT_I18N_MODULE_ID = "@nuxtjs/i18n"
export const parallelPlugin = false
export const isSSG = false
export const hasPages = true

export const DEFAULT_COOKIE_KEY = "i18n_redirected"
export const DEFAULT_DYNAMIC_PARAMS_KEY = "nuxtI18nInternal"
export const SWITCH_LOCALE_PATH_LINK_IDENTIFIER = "nuxt-i18n-slp"
/** client **/
if(import.meta.hot) {

function deepEqual(a, b, ignoreKeys = []) {
  // Same reference?
  if (a === b) return true

  // Check if either is null or not an object
  if (a == null || b == null || typeof a !== 'object' || typeof b !== 'object') {
    return false
  }

  // Get top-level keys, excluding ignoreKeys
  const keysA = Object.keys(a).filter(k => !ignoreKeys.includes(k))
  const keysB = Object.keys(b).filter(k => !ignoreKeys.includes(k))

  // Must have the same number of keys (after ignoring)
  if (keysA.length !== keysB.length) {
    return false
  }

  // Check each property
  for (const key of keysA) {
    if (!keysB.includes(key)) {
      return false
    }

    const valA = a[key]
    const valB = b[key]

    // Compare functions stringified
    if (typeof valA === 'function' && typeof valB === 'function') {
      if (valA.toString() !== valB.toString()) {
        return false
      }
    }
    // If nested, do a normal recursive check (no ignoring at deeper levels)
    else if (typeof valA === 'object' && typeof valB === 'object') {
      if (!deepEqual(valA, valB)) {
        return false
      }
    }
    // Compare primitive values
    else if (valA !== valB) {
      return false
    }
  }

  return true
}



async function loadCfg(config) {
  const nuxt = useNuxtApp()
  const { default: resolver } = await config()
  return typeof resolver === 'function' ? await nuxt.runWithContext(() => resolver()) : resolver
}




  import.meta.hot.accept("../i18n.config.js", async mod => {
    const [oldData, newData] = await Promise.all([loadCfg(vueI18nConfigs[0]), loadCfg(() => Promise.resolve(mod))]);
    vueI18nConfigs[0] = () => Promise.resolve(mod)
    if(deepEqual(oldData, newData, ['messages', 'numberFormats', 'datetimeFormats'])) {
      return await useNuxtApp()._nuxtI18nDev.resetI18nProperties()
    }
    import.meta.hot.send('i18n:options-complex-invalidation', {})
  })

}
/** client-end **/