import { defineNuxtPlugin } from '#app';
import VMdPreview from '@kangc/v-md-editor/lib/preview';
import githubTheme from '@kangc/v-md-editor/lib/theme/github';
import '@kangc/v-md-editor/lib/style/preview.css';
import '@kangc/v-md-editor/lib/theme/style/github.css';
import createKatexPlugin from '@kangc/v-md-editor/lib/plugins/katex/cdn';
import createMermaidPlugin from '@kangc/v-md-editor/lib/plugins/mermaid/cdn';
import hljs from 'highlight.js';
export default defineNuxtPlugin((nuxtApp) => {
  VMdPreview.use(githubTheme, {
    Hljs: hljs
  }).use(createKatexPlugin()).use(createMermaidPlugin());
  nuxtApp.vueApp.use(VMdPreview);
});
