// 导入请求方法，区分服务端和客户端
import { serverRequest, default as requestDify } from "@/utils/requestDify";
import axios from "axios";

// 判断运行环境
const isServer = typeof window === "undefined";

// 服务端请求包装器（统一处理服务端调用）
const serverRequestWrapper = async (config, event) => {
  if (isServer && !event) {
    throw new Error("Server request requires an event object");
  }
  return isServer ? serverRequest(config, event) : requestDify(config);
};

// 获取应用参数
export const getParameters = (data, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/v1/parameters`, method: "get", data },
    event
  );
};
// 获取sitemao
export const getSiteMap = (event) => {
  return serverRequestWrapper(
    { url: `/ai-base/index/getSiteMap`, method: "get" },
    event
  );
};
// 执行聊天
export const execChat = (data, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/v1/chat-messages`, method: "post", data },
    event
  );
};

// 中断聊天
export const stopChat = (data, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/v1/chat-messages/${data.task_id}/stop`, method: "post", data },
    event
  );
};

// 执行 workflow
export const execWorkflow = (data, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/v1/workflows/run`, method: "post", data },
    event
  );
};

// 获取科研工具
export const getTools = (data, event) => {
  return serverRequestWrapper(
    {
      url: `/ai-base/index/getAppsByTag?tag=科研工具`,
      method: "get",
      data,
    },
    event
  );
};

// 根据主站登录后信息获取双 token
export const mainLogin = (data, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/index/getAiWriteToken`, method: "post", data },
    event
  );
};

// 获取单个实例信息
export const getAppPrompt = (data, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/index/appParams`, method: "post", data },
    event
  );
};

// 获取单个实例会话
export const getAppConversations = (data, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/index/conversations`, method: "post", data, noLoading: true },
    event
  );
};

// 获取会话历史消息
export const getAppMessages = (data, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/v1/messages`, method: "get", data },
    event
  );
};

// 获取应用列表
export const getAppList = (data, event) => {
  return serverRequestWrapper(
    {
      url: `/ai-base/index/getAppList`,
      method: "post",
      data,
    },
    event
  );
};

// 获取 tab
export const getAppTypes = (languages, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/index/getAppTypes`, method: "get" },
    event
  );
};

// 获取访问者国家
export const getLocation = (event) => {
  return serverRequestWrapper(
    { url: `/ai-base/index/getLocation`, method: "get" },
    event
  );
};

// 获取语言配置
export const getAppLangs = (event) => {
  return serverRequestWrapper(
    { url: `/ai-base/index/getAppLangs`, method: "get" },
    event
  );
};

// 获取语言包
export const getConfigPage = (languages, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/index/getConfigPage`, method: "get" },
    event
  );
};

// 获取配置页图片
export const getConfigPageImg = (configName, event) => {
  return serverRequestWrapper(
    {
      url: `/ai-base/index/getConfigPage?configName=${configName}`,
      method: "get",
    },
    event
  );
};

// 获取 app 点击数量
export const getAppClickNum = (appUuid, openid, event) => {
  return serverRequestWrapper(
    {
      url: `/ai-base/index/getAppClickNum?appUuid=${appUuid}&openid=${openid}`,
      method: "get",
    },
    event
  );
};

// 获取授权登录地址
export const socialAuthRedirect = (socialType, event) => {
  return serverRequestWrapper(
    {
      url: `/ai-base/index/socialAuthRedirect?socialType=${socialType}`,
      method: "get",
    },
    event
  );
};

// 社交登录
export const socialLogin = (socialType, code, state, event) => {
  return serverRequestWrapper(
    {
      url: `/ai-base/index/socialLogin?socialType=${socialType}&code=${code}&state=${state}`,
      method: "get",
    },
    event
  );
};

// 退出登录
export const aiLogout = (event) => {
  return serverRequestWrapper(
    { url: `/ai-base/index/logout`, method: "post" },
    event
  );
};

// 创建订阅链接
export const createSubscription = (data, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/appUser/createSubscription`, method: "post", data },
    event
  );
};

// 登录
export const login = (data, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/index/login`, method: "post", data },
    event
  );
};

// 注册
export const register = (data, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/index/register`, method: "post", data },
    event
  );
};

// 发送验证码
export const sendEmailCode = (data, event) => {
  return serverRequestWrapper(
    {
      url: `/ai-base/index/sendEmailCode?email=${data.email}&type=${data.type}`,
      method: "post",
    },
    event
  );
};

// 二维码跳转支付宝链接
export const createAliSub = (data, event) => {
  return serverRequestWrapper(
    {
      url: `/ai-base/index/createAliSub?piId=${data.piId}&socialUserId=${data.socialUserId}&openid=${data.openid}&sessionId=${data.sessionId}`,
      method: "get",
    },
    event
  );
};

// 查询订单支付状态
export const getSubOrder = (piId, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/index/getSubOrder?piId=${piId}`, method: "get", noLoading: true },
    event
  );
};

// 上传文件
export const upload = (data, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/v1/files/upload`, method: "post", data },
    event
  );
};

// 根据 UUID 查询应用
export const getAppByUuid = (appUuid, event) => {
  return serverRequestWrapper(
    {
      url: `/ai-base/index/getAppByUuid?appUuid=${appUuid}`,
      method: "get",
      noLoading: true,
    },
    event
  );
};

// 获取 GraphQL token
export const getHToken = (event) => {
  return serverRequestWrapper(
    { url: `/ai-base/index/getHToken`, method: "get", noLoading: true },
    event
  );
};

// 获取文章数据（含SEO信息）
export const getArticleWithSEO = (id, event) => {
    return serverRequestWrapper({
            url: `/ai-base/index/snapshot/getArticleWithSEO?id=${id}`,method: "get", noLoading: true},
        event
    );
};

// 获取配置的试用次数
export const freeLimit = (event) => {
  return serverRequestWrapper({
          url: `/ai-base/index/free-limit`,method: "get", noLoading: true},
      event
  );
};
// 获取配置的试用次数
export const getPackageByKey = (event) => {
  return serverRequestWrapper({
          url: `/ai-base/index/getPackageByKey`,method: "get", noLoading: true},
      event
  );
};

// 取消订阅
export const cancelSubscription = (data, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/appUser/cancelSubscription?appUuid=`, method: "post", data },
    event
  );
};

// 获取QA列表
export const qaList = (data, event) => {
  return serverRequestWrapper(
    { url: `/ai-base/index/qa-list`, method: "post", data },
    event
  );
};
