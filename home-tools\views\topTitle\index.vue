<template>
  <div class="p-4 h-full">
    <div class="bg-white box h-full flex">
      <!-- 左侧输入框 -->
      <div class="w-[50%] flex flex-col">
        <div
          class="text-[16px] px-6 font-bold text-gray-600 h-[50px] flex items-center border border-solid border-gray-300 border-top-0 border-left-0 border-right-0"
        >
          输入摘要
        </div>
        <el-input
          class="p-6 flex-1"
          v-model="inputValue"
          type="textarea"
          placeholder=""
          show-word-limit
          resize="none"
        />
        <div class="flex justify-end mb-4 mr-4">
          <div
            class="text-white font-bold cursor-pointer rounded-md px-4 py-1 mr-4"
            :class="mode == 'classics' ? 'bg-[#499557]' : 'bg-gray-400'"
            @click="onMode('classics')"
          >
            经典
          </div>
          <div
            class="text-white font-bold cursor-pointer rounded-md px-4 py-1"
            :class="mode == 'light' ? 'bg-[#499557]' : 'bg-gray-400'"
            @click="onMode('light')"
          >
            眼前一亮
          </div>
        </div>
      </div>
      <!-- 右侧结果 -->
      <div class="flex-1 flex flex-col h-full overflow-hidden">
        <div
          class="text-[16px] px-6 font-bold text-gray-600 min-h-[50px] flex items-center border border-solid border-gray-300 border-top-0 border-left-0 border-right-0"
        >
          生成5个Title
        </div>
        <div class="border-r flex-1 p-4 flex flex-col overflow-hidden">
          <!-- title选择 -->
          <div class="flex items-center mb-4">
            <div
              class="cursor-pointer px-4 text-white mr-4"
              :class="index + 1 == titleActive ? 'bg-[#4d73be]' : 'bg-gray-400'"
              v-for="(item,index) in result"
              :key="index"
              @click="onTitleActive(item,index)"
            >
              {{ `Title${index + 1}` }}
            </div>
          </div>
          <!-- 结果展示 -->
          <el-empty description="暂无数据" v-if="!titleInfo"/>
          <div class="py-4 flex-1 overflow-auto" v-else>
            <div class="mb-4">
              <div class="mb-2 relative">
                <span class="text-[#0071bc]"
                  >{{ titleInfo.title }}</span
                >
                <el-icon
                  title="复制"
                  size="16"
                  color="#909399"
                  class="cursor-pointer ml-4 absolute top-1"
                  v-copy="titleInfo.title"
                  ><CopyDocument
                /></el-icon>
              </div>
              <!-- 翻译 -->
              <div class="text-gray-500 text-[12px]">
                {{ titleInfo.titleZh }}
              </div>
              <el-divider />
            </div>
            <!-- 参考文献 -->
            <div>
              <div class="text-gray-500 text-[12px] flex items-center mb-4">
                <span>参考文献（显示5条）</span>
                <el-tooltip
                  effect="dark"
                  content="以上Title由系统自动参照海量高分文献的主题内容生成，此处随机显示5条。"
                  placement="top"
                >
                  <el-icon color="#999" size="16" class="cursor-pointer"><QuestionFilled /></el-icon>
                </el-tooltip>
              </div>
              <div v-for="(item, index) in titleInfo.recArticles" :key="index">
                <div class="flex">
                  <div class="mr-2">
                    <div class="w-[22px] h-[22px] flex items-center justify-center px-1 bg-[#4d73be] text-white rounded-md">{{ index + 1 }}</div>
                  </div>
                  <div>
                    <div class="mb-2 relative">
                      <span v-html="item.title"></span>
                      <el-icon
                        title="复制"
                        size="16"
                        color="#909399"
                        class="cursor-pointer ml-4 absolute top-1"
                        v-copy="item.title.replace(/<[^>]+>/g,'')"
                        ><CopyDocument
                      /></el-icon>
                    </div>
                    <div class="text-gray-500 text-[12px]">{{ item.zh_title }}</div>
                  </div>
                </div>
                <el-divider v-if="index + 1 < titleInfo.recArticles.length" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { medsciAi } from "@/api/index";
import { CopyDocument, QuestionFilled } from "@element-plus/icons-vue";
import { shuffleArray } from "@/utils/index"
const inputValue = ref("");
const mode = ref(null);
const titleActive = ref(1);
const result = ref([]);
const titleInfo = ref(null)
// 模式选择
const onMode = (val) => {
  mode.value = val;
  titleActive.value = 1
  getData()
};
// Title选择
const onTitleActive = (val,index) => {
  titleActive.value = index + 1;
  titleInfo.value = val
};

// 
const getData = () => {
  if (!inputValue.value) return ElMessage.warning("请输入摘要")
  medsciAi('recommend-title', {
    text: inputValue.value,
    mode: mode.value
  }).then(res => {
    if (!res || !res.data) return
    result.value = res.data
    result.value = shuffleArray(result.value)
    titleInfo.value = result.value[0]
    titleInfo.value.recArticles = shuffleArray(titleInfo.value.recArticles)
  })
}
</script>

<style lang="scss" scoped>
.box {
  box-shadow: 0 4px 22px 0 rgba(0, 0, 0, 0.2);
  :deep(.el-textarea__inner) {
    outline: none;
    border: none;
    resize: none;
    box-shadow: none;
    background: none;
    padding: 0;
    height: 100%;
  }
  .border-r {
    border-left: 2px solid #e6e6e6;
  }
}
</style>
