import gql from "graphql-tag";

/*
    获取具体的字典分类下的字典词
    参数：
*/
export const getTreeListGql = gql`
  query getTreeListGql($path: String!, $status: String = "正常") {
    data: ai_base_ai_common_relations(where: {path: {_like: $path}, status: {_eq: $status}, type: {_in: ["字典分类-字典词","字典词-字典词"]}}) {
      relate_id
      entity_id
      path
      id
      status
      detail: dict_prompt {
        id
        prompt_name
        prompt_description
        prompt_category
        prompt_status
        dify_app_uuid
        directory_md
      }
    }
  }
`;