import axios from "axios";
import storage from "./storage";
import { ElMessage } from "element-plus";
import { $loading } from "@/utils/loading";
import cookie from "js-cookie";
const userId = cookie.get("userInfo")
  ? JSON.parse(cookie.get("userInfo"))?.userId
  : "";

// 创建axios实例
const service = axios.create({
  baseURL:"/",
  timeout: 1000 * 60 * 2,
  headers: { "Content-Type": "application/json;charset=utf-8" },
});

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    if(config.url.includes("v1/files/upload")){
      config.headers[
        'Content-Type']= 'multipart/form-data'
    }
    const deviceId =  null;
    const token = null;
    config.headers["Visitor-Code"] = deviceId;
    config.headers["User-Id"] = userId;
    if (config.url !== '/dev-api/ai-base/index/getAiWriteToken' && token) { 
      config.headers["Authorization"] = `Bearer ${token}`
    }

    // const TOKEN = "Bearer 1704e4c593b54b25ae74bd4773c0ad42";
    // if (TOKEN) config.headers["Authorization"] = TOKEN;
    if (config.method === "get") {
      config.params = config.data;
    }
    !config.noLoading && $loading.showLoading();
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response) => {
    const { status, data } = response;
    if (status === 200) {
      $loading.hideLoading();
      const code = data.code;
      switch (code) {
        case 0: // 成功
          if (data.data) {
              return Promise.resolve(data.data);
          } else {
            ElMessage.error("访问太火爆了！请稍后再试~");
            return Promise.reject("访问太火爆了！请稍后再试~");
          }
        case 401:
         
          break;
        default:
          console.error(data,code);
          Promise.reject(data);
          ElMessage.error(data.msg);
      }
    } else {
      ElMessage.error("访问太火爆了！请稍后再试~");
      Promise.reject("访问太火爆了！请稍后再试~");
    }
  },
  (error) => {
    console.error(error);
    $loading.hideLoading();
    return Promise.reject(error);
  }
);

export default service.request;
