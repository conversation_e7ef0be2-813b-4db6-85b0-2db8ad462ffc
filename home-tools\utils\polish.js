
var l = -1
  , o = 1
  , c = 0;
function generateFirstTextDiff(t, e) {
  var n,
    i = [],
    a = new ((function () {
      function t(t) {
        (t = t || {}),
          (this.Timeout = t.timeout || 1),
          (this.EditCost = t.editCost || 4);
      }
      return (
        t.Diff,
        (t.prototype.main = function (t, e, n, i) {
          void 0 === i &&
            (i =
              this.Timeout <= 0
                ? Number.MAX_VALUE
                : new Date().getTime() + 1e3 * this.Timeout);
          var r = i;
          if (null == t || null == e)
            throw new Error("Null input. (diff_main)");
          if (t == e) return t ? [[c, t]] : [];
          void 0 === n && (n = !0);
          var a = n,
            s = this.commonPrefix(t, e),
            l = t.substring(0, s);
            this.l = l;
          (t = t.substring(s)),
            (e = e.substring(s)),
            (s = this.commonSuffix(t, e));
          var o = t.substring(t.length - s);
          (t = t.substring(0, t.length - s)),
            (e = e.substring(0, e.length - s));
          var u = this.compute_(t, e, a, r);
          return (
            l && u.unshift([c, l]), o && u.push([c, o]), this.cleanupMerge(u), u
          );
        }),
        (t.prototype.compute_ = function (t, e, n, i) {
          var r;
          if (!t) return [[o, e]];
          if (!e) return [[
            l, t]];
          var a = t.length > e.length ? t : e,
            s = t.length > e.length ? e : t,
            u = a.indexOf(s);
          if (-1 != u)
            return (
              (r = [
                [o, a.substring(0, u)],
                [c, s],
                [o, a.substring(u + s.length)],
              ]),
              t.length > e.length && (r[0][0] = r[2][0] = l),
              r
            );
          if (1 == s.length)
            return [
              [l, t],
              [o, e],
            ];
          var h = this.halfMatch_(t, e);
          if (h) {
            var f = h[0],
              p = h[1],
              g = h[2],
              d = h[3],
              m = h[4],
              v = this.main(f, g, n, i),
              b = this.main(p, d, n, i);
            return v.concat([[c, m]], b);
          }
          return n && t.length > 100 && e.length > 100
            ? this.lineMode_(t, e, i)
            : this.bisect_(t, e, i);
        }),
        (t.prototype.lineMode_ = function (t, e, n) {
          (t = (p = this.linesToChars_(t, e)).chars1), (e = p.chars2);
          var i = p.lineArray,
            r = this.main(t, e, !1, n);
          this.charsToLines_(r, i), this.cleanupSemantic(r), r.push([c, ""]);
          for (var a = 0, s = 0, u = 0, h = "", f = ""; a < r.length; ) {
            switch (r[a][0]) {
              case o:
                u++, (f += r[a][1]);
                break;
              case l:
                s++, (h += r[a][1]);
                break;
              case c:
                if (s >= 1 && u >= 1) {
                  r.splice(a - s - u, s + u), (a = a - s - u);
                  for (
                    var p, g = (p = this.main(h, f, !1, n)).length - 1;
                    g >= 0;
                    g--
                  )
                    r.splice(a, 0, p[g]);
                  a += p.length;
                }
                (u = 0), (s = 0), (h = ""), (f = "");
            }
            a++;
          }
          return r.pop(), r;
        }),
        (t.prototype.bisect_ = function (t, e, n) {
          for (
            var i = t.length,
              r = e.length,
              a = Math.ceil((i + r) / 2),
              s = a,
              c = 2 * a,
              u = new Array(c),
              h = new Array(c),
              f = 0;
            f < c;
            f++
          )
            (u[f] = -1), (h[f] = -1);
          (u[s + 1] = 0), (h[s + 1] = 0);
          for (
            var p = i - r, g = p % 2 != 0, d = 0, m = 0, v = 0, b = 0, w = 0;
            w < a && !(new Date().getTime() > n);
            w++
          ) {
            for (var x = -w + d; x <= w - m; x += 2) {
              for (
                var _ = s + x,
                  T =
                    (N =
                      x == -w || (x != w && u[_ - 1] < u[_ + 1])
                        ? u[_ + 1]
                        : u[_ - 1] + 1) - x;
                N < i && T < r && t.charAt(N) == e.charAt(T);

              )
                N++, T++;
              if (((u[_] = N), N > i)) m += 2;
              else if (T > r) d += 2;
              else if (
                g &&
                (O = s + p - x) >= 0 &&
                O < c &&
                -1 != h[O] &&
                N >= (C = i - h[O])
              )
                return this.bisectSplit_(t, e, N, T, n);
            }
            for (var y = -w + v; y <= w - b; y += 2) {
              for (
                var C,
                  O = s + y,
                  k =
                    (C =
                      y == -w || (y != w && h[O - 1] < h[O + 1])
                        ? h[O + 1]
                        : h[O - 1] + 1) - y;
                C < i && k < r && t.charAt(i - C - 1) == e.charAt(r - k - 1);

              )
                C++, k++;
              if (((h[O] = C), C > i)) b += 2;
              else if (k > r) v += 2;
              else if (!g) {
                var N;
                if ((_ = s + p - y) >= 0 && _ < c && -1 != u[_])
                  if (((T = s + (N = u[_]) - _), N >= (C = i - C)))
                    return this.bisectSplit_(t, e, N, T, n);
              }
            }
          }
          return [
            [l, t],
            [o, e],
          ];
        }),
        (t.prototype.bisectSplit_ = function (t, e, n, i, r) {
          var a = t.substring(0, n),
            s = e.substring(0, i),
            l = t.substring(n),
            o = e.substring(i),
            c = this.main(a, s, !1, r),
            u = this.main(l, o, !1, r);
          return c.concat(u);
        }),
        (t.prototype.linesToChars_ = function (t, e) {
          var n = [],
            i = {};
          function r(t) {
            for (var e = "", r = 0, a = -1, s = n.length; a < t.length - 1; ) {
              -1 == (a = t.indexOf("\n", r)) && (a = t.length - 1);
              var l = t.substring(r, a + 1);
              (r = a + 1),
                (i.hasOwnProperty ? i.hasOwnProperty(l) : void 0 !== i[l])
                  ? (e += String.fromCharCode(i[l]))
                  : ((e += String.fromCharCode(s)), (i[l] = s), (n[s++] = l));
            }
            return e;
          }
          return (
            (n[0] = ""),
            {
              chars1: r(t),
              chars2: r(e),
              lineArray: n,
            }
          );
        }),
        (t.prototype.charsToLines_ = function (t, e) {
          for (var n = 0; n < t.length; n++) {
            for (var i = t[n][1], r = [], a = 0; a < i.length; a++)
              r[a] = e[i.charCodeAt(a)];
            t[n][1] = r.join("");
          }
        }),
        (t.prototype.commonPrefix = function (t, e) {
          if (!t || !e || t.charAt(0) != e.charAt(0)) return 0;
          for (
            var n = 0, i = Math.min(t.length, e.length), r = i, a = 0;
            n < r;

          )
            t.substring(a, r) == e.substring(a, r) ? (a = n = r) : (i = r),
              (r = Math.floor((i - n) / 2 + n));
          return r;
        }),
        (t.prototype.commonSuffix = function (t, e) {
          if (!t || !e || t.charAt(t.length - 1) != e.charAt(e.length - 1))
            return 0;
          for (
            var n = 0, i = Math.min(t.length, e.length), r = i, a = 0;
            n < r;

          )
            t.substring(t.length - r, t.length - a) ==
            e.substring(e.length - r, e.length - a)
              ? (a = n = r)
              : (i = r),
              (r = Math.floor((i - n) / 2 + n));
          return r;
        }),
        (t.prototype.commonOverlap_ = function (t, e) {
          var n = t.length,
            i = e.length;
          if (0 == n || 0 == i) return 0;
          n > i ? (t = t.substring(n - i)) : n < i && (e = e.substring(0, n));
          var r = Math.min(n, i);
          if (t == e) return r;
          for (var a = 0, s = 1; ; ) {
            var l = t.substring(r - s),
              o = e.indexOf(l);
            if (-1 == o) return a;
            (s += o),
              (0 != o && t.substring(r - s) != e.substring(0, s)) ||
                ((a = s), s++);
          }
        }),
        (t.prototype.halfMatch_ = function (t, e) {
          if (this.Timeout <= 0) return null;
          var n = t.length > e.length ? t : e,
            i = t.length > e.length ? e : t;
          if (n.length < 4 || 2 * i.length < n.length) return null;
          var r = this;
          function a(t, e, n) {
            for (
              var i,
                a,
                s,
                l,
                o = t.substring(n, n + Math.floor(t.length / 4)),
                c = -1,
                u = "";
              -1 != (c = e.indexOf(o, c + 1));

            ) {
              var h = r.commonPrefix(t.substring(n), e.substring(c)),
                f = r.commonSuffix(t.substring(0, n), e.substring(0, c));
              u.length < f + h &&
                ((u = e.substring(c - f, c) + e.substring(c, c + h)),
                (i = t.substring(0, n - f)),
                (a = t.substring(n + h)),
                (s = e.substring(0, c - f)),
                (l = e.substring(c + h)));
            }
            return 2 * u.length >= t.length ? [i, a, s, l, u] : null;
          }
          var s,
            l,
            o,
            c,
            u,
            h = a(n, i, Math.ceil(n.length / 4)),
            f = a(n, i, Math.ceil(n.length / 2));
          return h || f
            ? ((s = f ? (h && h[4].length > f[4].length ? h : f) : h),
              t.length > e.length
                ? ((l = s[0]), (o = s[1]), (c = s[2]), (u = s[3]))
                : ((c = s[0]), (u = s[1]), (l = s[2]), (o = s[3])),
              [l, o, c, u, s[4]])
            : null;
        }),
        (t.prototype.cleanupSemantic = function (t) {
          for (
            var e = !1,
              n = [],
              i = 0,
              r = null,
              a = 0,
              s = 0,
              u = 0,
              h = 0,
              f = 0;
            a < t.length;

          )
            t[a][0] == c
              ? ((n[i++] = a),
                (s = h),
                (u = f),
                (h = 0),
                (f = 0),
                (r = t[a][1]))
              : (t[a][0] == o ? (h += t[a][1].length) : (f += t[a][1].length),
                r &&
                  r.length <= Math.max(s, u) &&
                  r.length <= Math.max(h, f) &&
                  (t.splice(n[i - 1], 0, [l, r]),
                  (t[n[i - 1] + 1][0] = o),
                  i--,
                  (a = --i > 0 ? n[i - 1] : -1),
                  (s = 0),
                  (u = 0),
                  (h = 0),
                  (f = 0),
                  (r = null),
                  (e = !0))),
              a++;
          for (
            e && this.cleanupMerge(t), this.cleanupSemanticLossless(t), a = 1;
            a < t.length;

          ) {
            if (t[a - 1][0] == l && t[a][0] == o) {
              var p = t[a - 1][1],
                g = t[a][1],
                d = this.commonOverlap_(p, g),
                m = this.commonOverlap_(g, p);
              d >= m
                ? (d >= p.length / 2 || d >= g.length / 2) &&
                  (t.splice(a, 0, [c, g.substring(0, d)]),
                  (t[a - 1][1] = p.substring(0, p.length - d)),
                  (t[a + 1][1] = g.substring(d)),
                  a++)
                : (m >= p.length / 2 || m >= g.length / 2) &&
                  (t.splice(a, 0, [c, p.substring(0, m)]),
                  (t[a - 1][0] = o),
                  (t[a - 1][1] = g.substring(0, g.length - m)),
                  (t[a + 1][0] = l),
                  (t[a + 1][1] = p.substring(m)),
                  a++),
                a++;
            }
            a++;
          }
        }),
        (t.prototype.cleanupSemanticLossless = function (e) {
          function n(e, n) {
            if (!e || !n) return 6;
            var i = e.charAt(e.length - 1),
              r = n.charAt(0),
              a = i.match(t.nonAlphaNumericRegex_),
              s = r.match(t.nonAlphaNumericRegex_),
              l = a && i.match(t.whitespaceRegex_),
              o = s && r.match(t.whitespaceRegex_),
              c = l && i.match(t.linebreakRegex_),
              u = o && r.match(t.linebreakRegex_),
              h = c && e.match(t.blanklineEndRegex_),
              f = u && n.match(t.blanklineStartRegex_);
            return h || f
              ? 5
              : c || u
              ? 4
              : a && !l && o
              ? 3
              : l || o
              ? 2
              : a || s
              ? 1
              : 0;
          }
          for (var i = 1; i < e.length - 1; ) {
            if (e[i - 1][0] == c && e[i + 1][0] == c) {
              var r = e[i - 1][1],
                a = e[i][1],
                s = e[i + 1][1],
                l = this.commonSuffix(r, a);
              if (l) {
                var o = a.substring(a.length - l);
                (r = r.substring(0, r.length - l)),
                  (a = o + a.substring(0, a.length - l)),
                  (s = o + s);
              }
              for (
                var u = r, h = a, f = s, p = n(r, a) + n(a, s);
                a.charAt(0) === s.charAt(0);

              ) {
                (r += a.charAt(0)),
                  (a = a.substring(1) + s.charAt(0)),
                  (s = s.substring(1));
                var g = n(r, a) + n(a, s);
                g >= p && ((p = g), (u = r), (h = a), (f = s));
              }
              e[i - 1][1] != u &&
                (u ? (e[i - 1][1] = u) : (e.splice(i - 1, 1), i--),
                (e[i][1] = h),
                f ? (e[i + 1][1] = f) : (e.splice(i + 1, 1), i--));
            }
            i++;
          }
        }),
        (t.nonAlphaNumericRegex_ = /[^a-zA-Z0-9]/),
        (t.whitespaceRegex_ = /\s/),
        (t.linebreakRegex_ = /[\r\n]/),
        (t.blanklineEndRegex_ = /\n\r?\n$/),
        (t.blanklineStartRegex_ = /^\r?\n\r?\n/),
        (t.prototype.cleanupEfficiency = function (t) {
          for (
            var e = !1,
              n = [],
              i = 0,
              r = null,
              a = 0,
              s = !1,
              u = !1,
              h = !1,
              f = !1;
            a < t.length;

          )
            t[a][0] == c
              ? (t[a][1].length < this.EditCost && (h || f)
                  ? ((n[i++] = a), (s = h), (u = f), (r = t[a][1]))
                  : ((i = 0), (r = null)),
                (h = f = !1))
              : (t[a][0] == l ? (f = !0) : (h = !0),
                r &&
                  ((s && u && h && f) ||
                    (r.length < this.EditCost / 2 && s + u + h + f == 3)) &&
                  (t.splice(n[i - 1], 0, [l, r]),
                  (t[n[i - 1] + 1][0] = o),
                  i--,
                  (r = null),
                  s && u
                    ? ((h = f = !0), (i = 0))
                    : ((a = --i > 0 ? n[i - 1] : -1), (h = f = !1)),
                  (e = !0))),
              a++;
          e && this.cleanupMerge(t);
        }),
        (t.prototype.cleanupMerge = function (t) {
          t.push([c, ""]);
          for (var e, n = 0, i = 0, r = 0, a = "", s = ""; n < t.length; )
            switch (t[n][0]) {
              case o:
                r++, (s += t[n][1]), n++;
                break;
              case l:
                i++, (a += t[n][1]), n++;
                break;
              case c:
                i + r > 1
                  ? (0 !== i &&
                      0 !== r &&
                      (0 !== (e = this.commonPrefix(s, a)) &&
                        (n - i - r > 0 && t[n - i - r - 1][0] == c
                          ? (t[n - i - r - 1][1] += s.substring(0, e))
                          : (t.splice(0, 0, [c, s.substring(0, e)]), n++),
                        (s = s.substring(e)),
                        (a = a.substring(e))),
                      0 !== (e = this.commonSuffix(s, a)) &&
                        ((t[n][1] = s.substring(s.length - e) + t[n][1]),
                        (s = s.substring(0, s.length - e)),
                        (a = a.substring(0, a.length - e)))),
                    0 === i
                      ? t.splice(n - r, i + r, [o, s])
                      : 0 === r
                      ? t.splice(n - i, i + r, [l, a])
                      : t.splice(n - i - r, i + r, [l, a], [o, s]),
                    (n = n - i - r + (i ? 1 : 0) + (r ? 1 : 0) + 1))
                  : 0 !== n && t[n - 1][0] == c
                  ? ((t[n - 1][1] += t[n][1]), t.splice(n, 1))
                  : n++,
                  (r = 0),
                  (i = 0),
                  (a = ""),
                  (s = "");
            }
          "" === t[t.length - 1][1] && t.pop();
          var u = !1;
          for (n = 1; n < t.length - 1; )
            t[n - 1][0] == c &&
              t[n + 1][0] == c &&
              (t[n][1].substring(t[n][1].length - t[n - 1][1].length) ==
              t[n - 1][1]
                ? ((t[n][1] =
                    t[n - 1][1] +
                    t[n][1].substring(0, t[n][1].length - t[n - 1][1].length)),
                  (t[n + 1][1] = t[n - 1][1] + t[n + 1][1]),
                  t.splice(n - 1, 1),
                  (u = !0))
                : t[n][1].substring(0, t[n + 1][1].length) == t[n + 1][1] &&
                  ((t[n - 1][1] += t[n + 1][1]),
                  (t[n][1] =
                    t[n][1].substring(t[n + 1][1].length) + t[n + 1][1]),
                  t.splice(n + 1, 1),
                  (u = !0))),
              n++;
          u && this.cleanupMerge(t);
        }),
        (t.prototype.xIndex = function (t, e) {
          var n,
            i = 0,
            r = 0,
            a = 0,
            s = 0;
          for (
            n = 0;
            n < t.length &&
            (t[n][0] !== o && (i += t[n][1].length),
            t[n][0] !== l && (r += t[n][1].length),
            !(i > e));
            n++
          )
            (a = i), (s = r);
          return t.length != n && t[n][0] === l ? s : s + (e - a);
        }),
        (t.prototype.prettyHtml = function (t) {
          for (
            var e = [], n = /&/g, i = /</g, r = />/g, a = /\n/g, s = 0;
            s < t.length;
            s++
          ) {
            var u = t[s][0],
              h = t[s][1]
                .replace(n, "&amp;")
                .replace(i, "&lt;")
                .replace(r, "&gt;")
                .replace(a, "<br/>");
            switch (u) {
              case o:
                e[s] = "<ins>" + h + "</ins>";
                break;
              case l:
                e[s] = "<del>" + h + "</del>";
                break;
              case c:
                e[s] = "<span>" + h + "</span>";
            }
          }
          return e.join("");
        }),
        (t.prototype.text1 = function (t) {
          for (var e = [], n = 0; n < t.length; n++)
            t[n][0] !== o && (e[n] = t[n][1]);
          return e.join("");
        }),
        (t.prototype.text2 = function (t) {
          for (var e = [], n = 0; n < t.length; n++)
            t[n][0] !== l && (e[n] = t[n][1]);
          return e.join("");
        }),
        (t.prototype.levenshtein = function (t) {
          for (var e = 0, n = 0, i = 0, r = 0; r < t.length; r++) {
            var a = t[r][0],
              s = t[r][1];
            switch (a) {
              case o:
                n += s.length;
                break;
              case l:
                i += s.length;
                break;
              case c:
                (e += Math.max(n, i)), (n = 0), (i = 0);
            }
          }
          return (e += Math.max(n, i));
        }),
        (t.prototype.toDelta = function (t) {
          for (var e = [], n = 0; n < t.length; n++)
            switch (t[n][0]) {
              case o:
                e[n] = "+" + encodeURI(t[n][1]);
                break;
              case l:
                e[n] = "-" + t[n][1].length;
                break;
              case c:
                e[n] = "=" + t[n][1].length;
            }
          return e.join("\t").replace(/%20/g, " ");
        }),
        (t.prototype.fromDelta = function (t, e) {
          for (
            var n = [], i = 0, r = 0, a = e.split(/\t/g), s = 0;
            s < a.length;
            s++
          ) {
            var u = a[s].substring(1);
            switch (a[s].charAt(0)) {
              case "+":
                try {
                  n[i++] = [o, decodeURI(u)];
                } catch (t) {
                  throw new Error("Illegal escape in diff_fromDelta: " + u);
                }
                break;
              case "-":
              case "=":
                var h = parseInt(u, 10);
                if (isNaN(h) || h < 0)
                  throw new Error("Invalid number in diff_fromDelta: " + u);
                var f = t.substring(r, (r += h));
                "=" == a[s].charAt(0) ? (n[i++] = [c, f]) : (n[i++] = [l, f]);
                break;
              default:
                if (a[s])
                  throw new Error(
                    "Invalid diff operation in diff_fromDelta: " + a[s]
                  );
            }
          }
          if (r != t.length)
            throw new Error(
              "Delta length (" +
                r +
                ") does not equal source text length (" +
                t.length +
                ")."
            );
          return n;
        }),
        t
      );
    })())();
  return (
    (i = a.main(t, e)),
    a.cleanupSemantic(i),
    (n = []),
    i.forEach(function (t) {
      for (var e = t[0], i = t[1].split("\n"), r = 0; r < i.length; r++)
        0 === r ? n.push([e, i[r]]) : n.push([e, "\n".concat(i[r])]);
    }),
    {
      textDiff: (i = n),
      // textDiffOriginal: i.map(function (t) {
      //   return (0, r.default)(t);
      // }),
    }
  );
}

function addKey(t) {
  var e = -1;
  return t.replace(/<(ins|del|span)>/g, function (t) {
    return e++, "<".concat(t.slice(1, -1), ' key="').concat(e, '">');
  });
}
function myOwnPretty(t) {
  for (
    var e = [], n = /&/g, i = /</g, r = />/g, a = /\n/g, s = 0;
    s < t.length;
    s++
  ) {
    var u = t[s][0],
      h = t[s][1]
        .replace(n, "&amp;")
        .replace(i, "&lt;")
        .replace(r, "&gt;")
        .replace(a, "<br/>");
    switch (u) {
      case o:
        e[s] =
          "<ins style='background-color:rgba(0,145,255,0);color:#ff0101;text-decoration: underline;' >" +
          h +
          "</ins>";
        break;
      case l:
        e[s] =
          "<del style='background: rgba(0,145,255,0.2);color:#0091FF;' >" +
          h +
          "</del>";
        break;
      case c:
        e[s] = "<span>" + h + "</span>";
    }
  }
  return e.join("");
}
function generateMarkup(t) {
  return "<div>" + addKey(myOwnPretty(t)).replace(/\n/g, "<br>") + "</div>";
}
function getDiffResult(t, e) {
  var n, i;
  (n = e),
    [
      "Here's the corrected ",
      "Here's a corrected ",
      "Here's the proofread ",
      "Here's a proofread",
      "Here is a proofread",
      "Here's a possible revision",
    ].forEach(function (t) {
      if (n.toLowerCase().startsWith(t.toLowerCase())) {
        var e = n.indexOf(":", t.length);
        n = n.substring(e + 1);
      }
    }),
    (e =
      '"' === (i = n = n.trim())[0] && '"' === i[i.length - 1]
        ? i.slice(1, i.length - 1)
        : i);

  var r = generateFirstTextDiff(t, e),
    a = r.textDiff;
  // r.textDiffOriginal;
  return generateMarkup(a);
}

export default getDiffResult;

// f.default.varNotNull(n.polish)) {
//   r[n.getCacheKey(i)] = s;
//   var o = n.polish;
//   o = f.default.Base64.encode(o),
//   o = f.default.reverseString(o),
//   n.getTrans(o).then(function(t) {
//       n.polishTrans = t,
//       s.polishTrans = t
//   })
// }
// polishWarp("r", e.temperature, 0, 1, null, !1, t)
// polishWarp: function(t, e, n, i, a, o, c) {
//   return new Promise(function(u, h) {
//       var f = {
//           text: t
//       };
//       s.default.varNotNull(e) && (f.temperature = e),
//       s.default.varNotNull(c) && (f.tr = c),
//       s.default.varNotNull(a) && (f.aync = a),
//       s.default.varNotNull(n) && (f.isTrans = n),
//       s.default.varNotNull(i) && (f.remain = i),
//       r.default.POST(l + "/protect/pub/aimode/polish", f, function(t) {
//           u(t)
//       }, function(t) {
//           h(t)
//       }, null, o)
//   }
//   )
// },
