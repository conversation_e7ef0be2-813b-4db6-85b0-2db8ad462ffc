const N = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
const a = (function (e) {
  for (var t = {}, n = 0, M = e.length; n < M; n++) t[e.charAt(n)] = n;
  return t;
})(N);
const s = String.fromCharCode;
// encode start
const o = /[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g;
const c = function (e) {
  if (e.length < 2)
    return (t = e.charCodeAt(0)) < 128
      ? e
      : t < 2048
      ? s(192 | (t >>> 6)) + s(128 | (63 & t))
      : s(224 | ((t >>> 12) & 15)) +
        s(128 | ((t >>> 6) & 63)) +
        s(128 | (63 & t));
  var t = 65536 + 1024 * (e.charCodeAt(0) - 55296) + (e.charCodeAt(1) - 56320);
  return (
    s(240 | ((t >>> 18) & 7)) +
    s(128 | ((t >>> 12) & 63)) +
    s(128 | ((t >>> 6) & 63)) +
    s(128 | (63 & t))
  );
};
const u = function(e) {
  return e.replace(o, c)
}
const D = function(e) {
  var t = [0, 2, 1][e.length % 3]
    , n = e.charCodeAt(0) << 16 | (e.length > 1 ? e.charCodeAt(1) : 0) << 8 | (e.length > 2 ? e.charCodeAt(2) : 0);
  return [N.charAt(n >>> 18), N.charAt(n >>> 12 & 63), t >= 2 ? "=" : N.charAt(n >>> 6 & 63), t >= 1 ? "=" : N.charAt(63 & n)].join("")
}
const g = function(e) {
  if (e.match(/[^\x00-\xFF]/))
      throw new RangeError("The string contains invalid characters.");
  return e.replace(/[\s\S]{1,3}/g, D)
}
const T = function(e) {
  return g(u(String(e)))
}
const j = function(e) {
  return e.replace(/[+\/]/g, function(e) {
      return "+" == e ? "-" : "_"
  }).replace(/=/g, "")
}
const l = function(e, t) {
  return t ? j(T(e)) : T(e)
}
// encode end

// decode start
const y =
  /[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g;
const I = function (e) {
  switch (e.length) {
    case 4:
      var t =
        (((7 & e.charCodeAt(0)) << 18) |
          ((63 & e.charCodeAt(1)) << 12) |
          ((63 & e.charCodeAt(2)) << 6) |
          (63 & e.charCodeAt(3))) -
        65536;
      return s(55296 + (t >>> 10)) + s(56320 + (1023 & t));
    case 3:
      return s(
        ((15 & e.charCodeAt(0)) << 12) |
          ((63 & e.charCodeAt(1)) << 6) |
          (63 & e.charCodeAt(2))
      );
    default:
      return s(((31 & e.charCodeAt(0)) << 6) | (63 & e.charCodeAt(1)));
  }
};
const w = function (e) {
  return e.replace(y, I);
};
const m = function (e) {
  return String(e)
    .replace(/[-_]/g, function (e) {
      return "-" == e ? "+" : "/";
    })
    .replace(/[^A-Za-z0-9\+\/]/g, "");
};
const d = function (e) {
  var t = e.length,
    n = t % 4,
    M =
      (t > 0 ? a[e.charAt(0)] << 18 : 0) |
      (t > 1 ? a[e.charAt(1)] << 12 : 0) |
      (t > 2 ? a[e.charAt(2)] << 6 : 0) |
      (t > 3 ? a[e.charAt(3)] : 0),
    i = [s(M >>> 16), s((M >>> 8) & 255), s(255 & M)];
  return (i.length -= [0, 0, 2, 1][n]), i.join("");
};
const O = function (e) {
  return e.replace(/\S{1,4}/g, d);
};
const Base64 = {
  decode: function (e) {
    return (function (e) {
      return w(O(e));
    })(m(e));
  },
};
function byteToString(e) {
  if ("string" == typeof e) return e;
  let t = "",
    n = e;
  for (let e = 0; e < n.length; e++) {
    let M = n[e].toString(2),
      i = M.match(/^1+?(?=0)/);
    if (i && 8 == M.length) {
      let M = i[0].length,
        r = n[e].toString(2).slice(7 - M);
      for (let t = 1; t < M; t++) r += n[t + e].toString(2).slice(2);
      (t += String.fromCharCode(parseInt(r, 2))), (e += M - 1);
    } else t += String.fromCharCode(n[e]);
  }
  return t;
}
function stringToByte(e) {
  let t,
    n,
    M = new Array();
  t = e.length;
  for (let i = 0; i < t; i++)
    (n = e.charCodeAt(i)) >= 65536 && n <= 1114111
      ? (M.push(((n >> 18) & 7) | 240),
        M.push(((n >> 12) & 63) | 128),
        M.push(((n >> 6) & 63) | 128),
        M.push((63 & n) | 128))
      : n >= 2048 && n <= 65535
      ? (M.push(((n >> 12) & 15) | 224),
        M.push(((n >> 6) & 63) | 128),
        M.push((63 & n) | 128))
      : n >= 128 && n <= 2047
      ? (M.push(((n >> 6) & 31) | 192), M.push((63 & n) | 128))
      : M.push(255 & n);
  return M;
}
// decode end

// 加密
export const encode = (e) => {
  return l(e)
};
// 解密
export const decode = (e) => {
  try {
    e = JSON.parse(e);
  } catch (e) {}
  let t = stringToByte((e = Base64.decode(e)));
  (e = byteToString((t = t.map((e) => e + 3)))),
    (t = stringToByte((e = Base64.decode(e))));
  for (let e = 0; e < t.length - 1; e += 2) t[e] ^= t[e + 1];
  (e = byteToString(t)),
    (e = byteToString(
      (t = (t = stringToByte((e = Base64.decode(e)))).map((e) => e + 5))
    )),
    (e = Base64.decode(e));
  try {
    e = JSON.parse(e);
  } catch (t) {
    try {
      (e = '"' + e + '"'), (e = JSON.parse(e));
    } catch (e) {}
  }
  return e;
};
