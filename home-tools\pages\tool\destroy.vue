<template>
    <div class="clear-data-warning">
      <h1>Warning: Clear Site Data</h1>
      <p>This action will delete all site data displayed on your device.</p>
      <button @click="destory">Are you sure you want to continue?</button>
    </div>
  </template>
   
  <script setup>
    import { ElMessage } from 'element-plus'
   
    const destory = () => {
        ElMessage({
            message: 'Finish Clear Site Data',
            type: 'warning',
        })
    }
  </script>
   
  <style scoped>
  .clear-data-warning {
    text-align: center;
    margin: 20px;
    padding: 20px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #f9f9f9;
  }
   
  .clear-data-warning h1 {
    margin-top: 0;
  }

  .clear-data-warning p {
    text-align: center;
    margin: 20px;
    padding: 20px;
  }
   

  </style>