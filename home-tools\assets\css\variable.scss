:root {
  //基础颜色
  --color-primary: #2e59a7; //主题色
  --color-success: #3eb370;
  --color-danger: #d36f69;
  --color-warning: #db8f52;
  --color-purple: #9a70be;
  --color-cyan: #28b5be;

  //字体颜色
  --font-color-dark: #333; //标题
  --font-color-subtitle: #808080; //副标题
  --font-color-lighter: #999; //辅助内容

  // 页面背景色
  --page-background-color: #f7f8fa;

  //字号
  --font-size-headline: 20px; //大标题
  --font-size-title: 16px; //标题
  --font-size-body: 14px; //正文
  --font-size-caption: 12px; //备注

  //行高
  @for $i from 1 through 20 {
    --line-height-#{2 * $i}: #{2 * $i}px;
  }
  --line-height-headline: 30px;
  --line-height-title: 24px;
  --line-height-body: 24px;
  --line-height-caption: 18px;

  //间距
  @for $i from 1 through 20 {
    --space-#{2 * $i}: #{2 * $i}px;
  }

  --left-aside-width: 212px; //左侧菜单栏宽度
  --header-height: 60px; //头部高度

  --border-color: #eaebed; // 边框线颜色
  --border-radius: 4px; //全局圆角

  //element-plus
  --el-menu-item-height: 56px; // 菜单item高度
  //菜单子item高度
  --el-menu-sub-item-height: calc(var(--el-menu-item-height) - 6px);
  --el-menu-item-font-size: 14px; // 菜单字体大小
}
