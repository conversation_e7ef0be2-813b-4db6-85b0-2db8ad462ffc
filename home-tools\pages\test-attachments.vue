<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4">
      <h1 class="text-3xl font-bold text-gray-900 mb-8">消息附件展示测试</h1>

      <!-- 测试用户消息 -->
      <div class="mb-8">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">用户消息示例</h2>
        <div class="ml-auto max-w-2xl bg-white border border-gray-200 rounded-2xl rounded-br-md p-4 shadow-sm">
          <div class="text-gray-800 text-base leading-relaxed">
            <p>请帮我分析这些医学资料，包括X光片、心电图视频和相关文档。</p>
          </div>
          <!-- 用户消息附件 -->
          <MessageAttachments :attachments="userAttachments" />
        </div>
      </div>

      <!-- 测试AI消息 -->
      <div class="mb-8">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">AI回复示例</h2>
        <div class="mr-auto max-w-4xl">
          <div class="text-base text-gray-800 leading-relaxed prose prose-base max-w-none">
            <p>根据您提供的医学资料，我已经完成了分析。以下是详细的分析报告和相关的参考资料：</p>
            <ul>
              <li>X光片显示肺部结构正常</li>
              <li>心电图显示心律规整</li>
              <li>建议定期复查</li>
            </ul>
          </div>
          <!-- AI消息附件 -->
          <MessageAttachments :attachments="aiAttachments" />
        </div>
      </div>

      <!-- 测试不同类型的附件 -->
      <div class="mb-8">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">各种类型附件示例（点击在新窗口打开）</h2>
        <div class="bg-white border border-gray-200 rounded-2xl p-4 shadow-sm">
          <p class="text-sm text-gray-600 mb-3">以下附件只显示基本信息，点击可在新窗口中打开：</p>
          <MessageAttachments :attachments="mixedAttachments" />
        </div>
      </div>

      <!-- 测试空附件 -->
      <div class="mb-8">
        <h2 class="text-xl font-semibold text-gray-800 mb-4">无附件消息</h2>
        <div class="bg-white border border-gray-200 rounded-2xl p-4 shadow-sm">
          <p class="text-gray-800">这是一条没有附件的普通消息。</p>
          <MessageAttachments :attachments="[]" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import MessageAttachments from '~/components/MessageAttachments/index.vue'

interface MessageFile {
  filename: string
  type: 'image' | 'video' | 'audio' | 'document'
  url: string
  size: number
}

// 用户消息附件示例
const userAttachments: MessageFile[] = [
  {
    filename: 'chest_xray.jpg',
    type: 'image',
    url: 'https://via.placeholder.com/400x300/4F46E5/FFFFFF?text=X%E5%85%89%E7%89%87',
    size: 1024000
  },
  {
    filename: 'ecg_video.mp4',
    type: 'video',
    url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
    size: 5120000
  },
  {
    filename: 'medical_report.pdf',
    type: 'document',
    url: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
    size: 2048000
  }
]

// AI回复附件示例
const aiAttachments: MessageFile[] = [
  {
    filename: 'analysis_report.pdf',
    type: 'document',
    url: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
    size: 1536000
  },
  {
    filename: 'reference_image.png',
    type: 'image',
    url: 'https://via.placeholder.com/300x200/10B981/FFFFFF?text=%E5%8F%82%E8%80%83%E5%9B%BE%E7%89%87',
    size: 768000
  }
]

// 混合类型附件示例
const mixedAttachments: MessageFile[] = [
  {
    filename: 'sample_image.jpg',
    type: 'image',
    url: 'https://via.placeholder.com/350x250/EF4444/FFFFFF?text=%E7%A4%BA%E4%BE%8B%E5%9B%BE%E7%89%87',
    size: 512000
  },
  {
    filename: 'sample_video.mp4',
    type: 'video',
    url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_640x360_1mb.mp4',
    size: 1024000
  },
  {
    filename: 'sample_audio.mp3',
    type: 'audio',
    url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    size: 256000
  },
  {
    filename: 'sample_document.docx',
    type: 'document',
    url: 'https://file-examples.com/storage/fe68c8c7c66d7b62c8e8e8e/2017/10/file_example_DOCX_10kB.docx',
    size: 10240
  },
  {
    filename: 'large_image.png',
    type: 'image',
    url: 'https://via.placeholder.com/800x600/8B5CF6/FFFFFF?text=%E5%A4%A7%E5%9B%BE%E7%89%87',
    size: 2048000
  },
  {
    filename: 'presentation.pptx',
    type: 'document',
    url: 'https://file-examples.com/storage/fe68c8c7c66d7b62c8e8e8e/2017/08/file_example_PPT_1MB.ppt',
    size: 1048576
  }
]

// 页面元数据
useHead({
  title: '消息附件渲染测试 - 梅斯小智',
  meta: [
    { name: 'description', content: '测试消息附件渲染组件的功能和样式' }
  ]
})
</script>

<style scoped>
.prose {
  @apply text-gray-800;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  @apply text-gray-900 font-semibold;
}

.prose p {
  @apply mb-4 leading-relaxed;
}

.prose ul,
.prose ol {
  @apply mb-4;
}

.prose li {
  @apply mb-2;
}
</style>
