<template>
  <div v-if="attachments && attachments.length > 0" class="message-attachments mt-3">
    <div class="attachments-grid">
      <div
        v-for="(attachment, index) in attachments"
        :key="index"
        class="attachment-item"
        :class="getAttachmentClass(attachment.type)"
      >
        <!-- 图片附件 -->
        <div v-if="attachment.type === 'image'" class="image-attachment">
          <img
            :src="attachment.url"
            :alt="attachment.filename"
            class="attachment-image"
            @click="previewImage(attachment)"
            @error="handleImageError"
          />
          <div class="attachment-info">
            <span class="attachment-name" :title="attachment.filename">
              {{ truncateFilename(attachment.filename) }}
            </span>
            <span class="attachment-size">{{ formatFileSize(attachment.size) }}</span>
          </div>
        </div>

        <!-- 视频附件 -->
        <div v-else-if="attachment.type === 'video'" class="video-attachment">
          <div class="video-container">
            <video
              :src="attachment.url"
              class="attachment-video"
              controls
              preload="metadata"
              @error="handleVideoError"
            >
              您的浏览器不支持视频播放
            </video>
          </div>
          <div class="attachment-info">
            <span class="attachment-name" :title="attachment.filename">
              {{ getFileIcon(attachment.type) }} {{ truncateFilename(attachment.filename) }}
            </span>
            <span class="attachment-size">{{ formatFileSize(attachment.size) }}</span>
          </div>
        </div>

        <!-- 音频附件 -->
        <div v-else-if="attachment.type === 'audio'" class="audio-attachment">
          <div class="audio-container">
            <audio
              :src="attachment.url"
              class="attachment-audio"
              controls
              preload="metadata"
              @error="handleAudioError"
            >
              您的浏览器不支持音频播放
            </audio>
          </div>
          <div class="attachment-info">
            <span class="attachment-name" :title="attachment.filename">
              {{ getFileIcon(attachment.type) }} {{ truncateFilename(attachment.filename) }}
            </span>
            <span class="attachment-size">{{ formatFileSize(attachment.size) }}</span>
          </div>
        </div>

        <!-- 文档附件 -->
        <div v-else class="document-attachment" @click="downloadAttachment(attachment)">
          <div class="document-icon">
            {{ getFileIcon(attachment.type) }}
          </div>
          <div class="attachment-info">
            <span class="attachment-name" :title="attachment.filename">
              {{ truncateFilename(attachment.filename) }}
            </span>
            <span class="attachment-size">{{ formatFileSize(attachment.size) }}</span>
          </div>
          <div class="download-hint">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
        </div>
      </div>
    </div>

    <!-- 图片预览模态框 -->
    <div
      v-if="previewImageData"
      class="image-preview-modal"
      @click="closeImagePreview"
    >
      <div class="image-preview-container" @click.stop>
        <img
          :src="previewImageData.url"
          :alt="previewImageData.filename"
          class="preview-image"
        />
        <button
          class="close-preview-btn"
          @click="closeImagePreview"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        <div class="preview-info">
          <span class="preview-filename">{{ previewImageData.filename }}</span>
          <span class="preview-size">{{ formatFileSize(previewImageData.size) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { formatFileSize, getFileIcon } from '~/utils/fileType'

interface MessageFile {
  filename: string
  type: 'image' | 'video' | 'audio' | 'document'
  url: string
  size: number
}

interface Props {
  attachments?: MessageFile[]
}

const props = withDefaults(defineProps<Props>(), {
  attachments: () => []
})

// 图片预览相关
const previewImageData = ref<MessageFile | null>(null)

// 截断文件名
const truncateFilename = (filename: string, maxLength: number = 20): string => {
  if (filename.length <= maxLength) return filename
  
  const extension = filename.split('.').pop() || ''
  const nameWithoutExt = filename.substring(0, filename.lastIndexOf('.'))
  const truncatedName = nameWithoutExt.substring(0, maxLength - extension.length - 4) + '...'
  
  return extension ? `${truncatedName}.${extension}` : truncatedName
}

// 获取附件样式类
const getAttachmentClass = (type: string): string => {
  return `attachment-${type}`
}

// 预览图片
const previewImage = (attachment: MessageFile) => {
  previewImageData.value = attachment
}

// 关闭图片预览
const closeImagePreview = () => {
  previewImageData.value = null
}

// 下载附件
const downloadAttachment = (attachment: MessageFile) => {
  const link = document.createElement('a')
  link.href = attachment.url
  link.download = attachment.filename
  link.target = '_blank'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 错误处理
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yMCAyOEMyNCA0IDI4IDggMjggMTJDMjggMTYgMjQgMjAgMjAgMjBDMTYgMjAgMTIgMTYgMTIgMTJDMTIgOCAxNiA0IDIwIDRaIiBzdHJva2U9IiM5Q0EzQUYiIHN0cm9rZS13aWR0aD0iMiIvPgo8L3N2Zz4K'
}

const handleVideoError = (event: Event) => {
  console.error('视频加载失败:', event)
}

const handleAudioError = (event: Event) => {
  console.error('音频加载失败:', event)
}
</script>

<style scoped>
.message-attachments {
  @apply border-t border-gray-100 pt-3;
}

.attachments-grid {
  @apply grid gap-3;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

.attachment-item {
  @apply rounded-lg border border-gray-200 overflow-hidden hover:shadow-md transition-all duration-200;
}

/* 图片附件样式 */
.image-attachment {
  @apply cursor-pointer;
}

.attachment-image {
  @apply w-full h-32 object-cover bg-gray-100;
}

/* 视频附件样式 */
.video-container {
  @apply bg-gray-100;
}

.attachment-video {
  @apply w-full h-32 object-cover;
}

/* 音频附件样式 */
.audio-container {
  @apply p-4 bg-gray-50;
}

.attachment-audio {
  @apply w-full;
}

/* 文档附件样式 */
.document-attachment {
  @apply p-4 cursor-pointer hover:bg-gray-50 flex items-center space-x-3;
}

.document-icon {
  @apply text-2xl flex-shrink-0;
}

.download-hint {
  @apply text-gray-400 ml-auto;
}

/* 附件信息样式 */
.attachment-info {
  @apply p-3 bg-white;
}

.attachment-name {
  @apply block text-sm font-medium text-gray-900 truncate;
}

.attachment-size {
  @apply block text-xs text-gray-500 mt-1;
}

/* 图片预览模态框 */
.image-preview-modal {
  @apply fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50;
}

.image-preview-container {
  @apply relative max-w-4xl max-h-full p-4;
}

.preview-image {
  @apply max-w-full max-h-full object-contain;
}

.close-preview-btn {
  @apply absolute top-4 right-4 text-white hover:text-gray-300 transition-colors;
}

.preview-info {
  @apply absolute bottom-4 left-4 text-white;
}

.preview-filename {
  @apply block text-sm font-medium;
}

.preview-size {
  @apply block text-xs opacity-75;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .attachments-grid {
    grid-template-columns: 1fr;
  }
  
  .attachment-image,
  .attachment-video {
    @apply h-24;
  }
}
</style>
