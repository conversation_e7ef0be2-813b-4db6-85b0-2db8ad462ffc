<template>
  <div v-if="attachments && attachments.length > 0" class="message-attachments mt-3">
    <div class="attachments-list">
      <div
        v-for="(attachment, index) in attachments"
        :key="index"
        class="attachment-item"
        @click="openAttachment(attachment)"
      >
        <div class="attachment-icon">
          {{ getFileIcon(attachment.type) }}
        </div>
        <div class="attachment-info">
          <span class="attachment-name" :title="attachment.filename">
            {{ attachment.filename }}
          </span>
          <span class="attachment-meta">
            <span class="attachment-size">{{ formatFileSize(attachment.size) }}</span>
          </span>
        </div>
        <div class="attachment-action">
          <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
          </svg>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { formatFileSize, getFileIcon } from '~/utils/fileType'

interface MessageFile {
  filename: string
  type: 'image' | 'video' | 'audio' | 'document'
  url: string
  size: number
}

interface Props {
  attachments?: MessageFile[]
}

const props = withDefaults(defineProps<Props>(), {
  attachments: () => []
})

// 打开附件
const openAttachment = (attachment: MessageFile) => {
  window.open(attachment.url, '_blank')
}
</script>

<style scoped>
.message-attachments {
  @apply border-t border-gray-100 pt-3;
}

.attachments-list {
  @apply space-y-2;
}

.attachment-item {
  @apply flex items-center p-3 bg-gray-50 rounded-lg border border-gray-200 hover:bg-gray-100 cursor-pointer transition-all duration-200;
}

.attachment-icon {
  @apply text-xl flex-shrink-0 mr-3;
}

.attachment-info {
  @apply flex-1 min-w-0;
}

.attachment-name {
  @apply block text-sm font-medium text-gray-900 truncate;
}

.attachment-meta {
  @apply flex items-center space-x-2 mt-1;
}

.attachment-type {
  @apply text-xs text-blue-600 bg-blue-100 px-2 py-0.5 rounded;
}

.attachment-size {
  @apply text-xs text-gray-500;
}

.attachment-action {
  @apply flex-shrink-0 ml-3;
}

/* 响应式设计 */
@media (max-width: 640px) {
  .attachment-item {
    @apply p-2;
  }

  .attachment-icon {
    @apply text-lg mr-2;
  }

  .attachment-name {
    @apply text-xs;
  }

  .attachment-type,
  .attachment-size {
    @apply text-xs;
  }
}
</style>
