// // ~/plugins/router.js
// export default defineNuxtPlugin((nuxtApp) => {
//     const router = useRouter()
//     // 监听路由错误
//     router.onError((error) => {
//       console.error('路由错误:', error)
//       navigateTo('/')
//     })
    
//     nuxtApp.hook('app:error', (err) => {
//       if (err.statusCode === 404) {
//         // 跳转到首页
//         window.location.href = '/'
//         console.log('err', err)
//       }
//     })
//   })