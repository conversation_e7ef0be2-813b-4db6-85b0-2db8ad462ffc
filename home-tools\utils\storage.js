const getStorage = () => {
    if (typeof window !== 'undefined') {
        return window.localStorage;
    }
    return {
        setItem: () => {},
        getItem: () => null,
        removeItem: () => {},
        clear: () => {}
    };
};

const STORAGE = getStorage();

const storage = {
    set: (key, val) => {
        if (typeof val !== 'string') {
            val = JSON.stringify(val)
        }
        STORAGE.setItem(key, val)
    },
    get: (key) => {
        return STORAGE.getItem(key);
    },
    remove: (key) => {
        STORAGE.removeItem(key);
    },
    clear: () => {
        STORAGE.clear();
    }
}

export default storage;