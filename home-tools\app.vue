<template>
  <div>
    <NuxtLayout>
      <NuxtPage />
    </NuxtLayout>
  </div>
</template>
<script setup>
import cookie from 'js-cookie';
import {Cookies} from '@/utils/cookieHandler'
import {
  mainLogin,
} from "@/api/base";
import { useI18n } from "vue-i18n";
    const { locale} = useI18n();
    const ai_apps_lang =  useCookie("ai_apps_lang",{domain:import.meta.env.VITE_MODE === "development" ?'localhost':import.meta.env.VITE_MODE === "test" ?".medon.com.cn" : ".medsci.cn",maxAge: 30 * 24 * 60 * 60 * 12});
    ai_apps_lang.value = locale.value;
    onMounted(() => {
      if(!location.href.includes('login') && !location.href.includes('sign-up')){
        window.sessionStorage.setItem('redirectUrl', window.location.href); 
      }
    })

</script>
<style>
  .el-loading-text{
    text-align: center;
  }
</style>