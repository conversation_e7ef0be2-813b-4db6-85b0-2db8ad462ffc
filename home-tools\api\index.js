import request from "@/utils/request";
// AI写作助手
export const aiSearchTitle = (type, data) => {
  return request({ url: `/paper/search/${type}`, method: "post", data });
};
// AI将重改写
export const sentence = (data) => {
  return request({ url: `/paper/rewrite/sentence`, method: "post", data });
};

export const medsciAi = (path, data) => {
  return request({ url: `/paper/${path}`, method: "post", data });
};
// 高分选题
export const ideaIgnite = (data) => {
  return request({ url: `/analysis/idea-ignite`, method: "post", data });
};
// 课题潜在思路
export const ideaIgniteCnsResult = (data) => {
  return request({
    url: `/analysis/idea-ignite/cns-result`,
    method: "post",
    data,
  });
};
export const ideaIgniteSentences = (data) => {
  return request({
    url: `/analysis/idea-ignite/sentences`,
    method: "post",
    data,
  });
};
// 课题思路设计
export const proAssistant = (data) => {
  return request({ url: `/analysis/dna/search`, method: "post", data });
};
// 国自然数据分析
export const nsfcSearch = (data) => {
  return request({ url: `/analysis/nsfc-search`, method: "post", data });
};
// 细胞系/动物模型查询助手
export const analysis = (data) => {
  return request({
    url: `/analysis/query-cell/recommend-rec`,
    method: "post",
    data,
  });
};
// 标题描述
export const analysisDesc = (data) => {
  return request({
    url: `/analysis/query-cell/recommend-rec-desc`,
    method: "post",
    data,
  });
};
// 实验设计助手
export const expDesign = (data) => {
  return request({ url: `/analysis/exp/design`, method: "post", data });
};
// 实验设计助手状态
export const expDesignState = (data) => {
  return request({
    url: `/analysis/exp/design/state`,
    method: "post",
    data,
    noLoading: true,
  });
};
// 上传
export const upLoad = (data) => {
  return request({
    url: `/test`,
    method: "post",
    data,
    headers: { "Content-Type": "multipart/form-data" },
  });
};
