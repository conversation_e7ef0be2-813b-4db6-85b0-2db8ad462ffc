import dayjs from 'dayjs'
/**
 * 防抖函数
 * @param func 需要包装的函数
 * @param delay 延迟时间，单位ms
 * @param immediate 是否默认执行一次(第一次不延迟)
 */
export const debounce = (func, delay, immediate = false) => {
  let timer = null;
  return (...args) => {
    if (immediate) {
      func.apply(this, args); // 确保引用函数的指向正确，并且函数的参数也不变
      immediate = false;
      return;
    }
    clearTimeout(timer);
    timer = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  };
};

/**
 * File转base64
 * @param file
 * @return base64
 * */
export function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    let fileResult = "";
    reader.readAsDataURL(file);
    //开始转
    reader.onload = () => {
      fileResult = reader.result;
    };
    //转失败
    reader.onerror = (error) => {
      reject(error);
    };
    //结束 resolve
    reader.onloadend = () => {
      resolve(fileResult);
    };
  });
}

/**
 * 获取今天日期
 * @param { string } delimiter 分割符
 * @returns string
 */
export function getTodayDate(delimiter = "-") {
  const date = new Date();
  const y = date.getFullYear();
  let m = date.getMonth() + 1;
  m = m < 10 ? "0" + m : m;
  let d = date.getDate();
  d = d < 10 ? "0" + d : d;
  return `${y}${delimiter}${m}${delimiter}${d}`;
}

/**
 * 时间日期转换
 * @param date 当前时间，new Date() 格式
 * @param format 需要转换的时间格式字符串
 * @description format 字符串随意，如 `YYYY-mm、YYYY-mm-dd`
 * @description format 季度："YYYY-mm-dd HH:MM:SS QQQQ"
 * @description format 星期："YYYY-mm-dd HH:MM:SS WWW"
 * @description format 几周："YYYY-mm-dd HH:MM:SS ZZZ"
 * @description format 季度 + 星期 + 几周："YYYY-mm-dd HH:MM:SS WWW QQQQ ZZZ"
 * @returns 返回拼接后的时间字符串
 */
export function formatDate(date, format) {
  // 日期不存在，则返回空
  if (!date) {
    return ''
  }
  // 日期存在，则进行格式化
  if (format === undefined) {
    format = 'YYYY-MM-DD HH:mm:ss'
  }
  return dayjs(date).format(format)
}


/**
 * 英文大小写转换
 * @param { string } str 需要转换的值
 * @param { number } type 转换类型
 * 默认全部大写
 * 1-首字母大写，其他小写
 * 2-首字母小写，其他大写
 * 3-大小写转换
 * 4-全部大写
 * 5-全部小写
 * @returns string
 */
export const letterConversion = (str, type = 4) => {
  switch (type) {
    case 1:
      return str.replace(/\b\w+\b/g, function (word) {
        return (
          word.substring(0, 1).toUpperCase() + word.substring(1).toLowerCase()
        );
      });
    case 2:
      return str.replace(/\b\w+\b/g, function (word) {
        return (
          word.substring(0, 1).toLowerCase() + word.substring(1).toUpperCase()
        );
      });
    case 3:
      return str
        .split("")
        .map(function (word) {
          if (/[a-z]/.test(word)) {
            return word.toUpperCase();
          } else {
            return word.toLowerCase();
          }
        })
        .join("");
    case 4:
      return str.toUpperCase();
    case 5:
      return str.toLowerCase();
    default:
      return str;
  }
};

/**
 * 获取assets静态资源
 * @param { string } path 路径
 * @returns string
 */
// utils/assetHelper.ts
export const getAssetsFile = (path) => {
  // 开发环境使用原始 assets 路径
  if (process.dev) {
    return `/img/${encodeURIComponent(path)}`;
  }
  
  // 生产环境使用构建后的路径
  return `/img/${encodeURIComponent(path)}`;
};


/**
 * 分组随机打乱
 * @param { number } group 组的个数 默认5组
 * @param { array } data 数据
 * @returns array
 */
export const shuffleArray = (data,group = 5) => {
  if (data && data.length > 0)  {
    // > 5 分组
    if (data.length > 5) {
      // 将数据分成group组
      let groupSize = Math.ceil(data.length / group);
      let groupedData = [];
      for (let i = 0; i < data.length; i += groupSize) {
        groupedData.push(data.slice(i, i + groupSize));
      }
      // 随机打乱每组内的数据
      groupedData.forEach((group) => {
        group.sort(() => Math.random() - 0.5);
      });
      // 二维转一维
      const flattenedData = groupedData.reduce((acc, val) => acc.concat(val), []);
      return flattenedData;
    } else {
      // 不分组 直接打乱
      const shuffledData = data.sort(() => Math.random() - 0.5);
      return shuffledData
    }
  } else {
    return [];
  }
};

export const injectCustomCode = (customCss,customJs) => {
  const style = document.createElement('style');
    style.type = 'text/css';
    style.innerHTML = customCss;
    document.head.appendChild(style);
    // 创建一个新的script元素
    var script = document.createElement('script');
    script.type = 'text/javascript';
    // 设置脚本内容
    script.text = customJs;
    // 将script元素添加到body的末尾
    document.body.appendChild(script);
}
