<template>
  <div class="pt-10 overflow-auto h-full">
    <el-input
      v-model="inputValue"
      :rows="8"
      type="textarea"
      placeholder="请输入不超过250单词的段落"
      show-word-limit
      resize="none"
      :maxlength="250"
    />
    <div class="flex justify-center my-10">
      <el-button type="primary" @click="getData">改 写</el-button>
    </div>
    <div v-if="list && list.length">
      <div class="flex justify-center mb-6 relative" v-for="(item,index) in list" :key="index">
        <span v-html="item.textShow" class="text-[18px]"></span>
        <el-icon
          title="复制"
          size="16"
          color="#909399"
          class="cursor-pointer ml-2 absolute"
          v-copy="item.text"
          ><CopyDocument
        /></el-icon>
      </div>
      <div class="flex justify-center mt-10">
        <el-button type="primary" link @click="change">换一换</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { sentence } from "@/api/index";
import { CopyDocument } from "@element-plus/icons-vue";
import { shuffleArray } from "@/utils/index"
const inputValue = ref('')
const result = ref([])
const list = ref([])
const changeType = ref(5)

// 获取数据
const getData = () => {
  let isChina = (s) => {
    var patrn = /[\u4E00-\u9FA5]|[\uFE30-\uFFA0]/gi;
    if (!patrn.exec(s)) {
      return false;
    } else {
      return true;
    }
  }
  if (isChina(inputValue.value) || !inputValue.value) {
    return ElMessage.warning("请输入英文内容");
  }
  sentence({text: inputValue.value}).then(res => {
    if (!res) return
    if (!res || !res.data) return
    result.value = res.data
    result.value = shuffleArray(result.value)
    changeType.value = 5
    change()
  })
}
// 换一换
const change = () => {
  let arr = []
  if (changeType.value == 5) {
    arr = [0,5]
    changeType.value = 3
  } else if (changeType.value == 3) {
    arr = [5,8]  
    changeType.value = 2
  } else if (changeType.value == 2) {
    arr = [8,10]  
    changeType.value = 5
  }
  list.value = JSON.parse(JSON.stringify(result.value)).slice(arr[0],arr[1])
}
</script>

<style lang="scss" scoped></style>
