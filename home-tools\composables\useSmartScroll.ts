/**
 * 智能滚动组合式函数
 * 用于AI回复的打字机效果显示时的自动滚动管理
 */

export interface SmartScrollOptions {
  /** 距离底部的阈值，小于此值时恢复自动滚动 */
  bottomThreshold?: number
  /** 滚动动画持续时间 */
  scrollDuration?: number
  /** 滚动行为类型 */
  scrollBehavior?: 'smooth' | 'auto'
  /** 检测用户滚动的防抖延迟 */
  userScrollDebounce?: number
  /** 是否启用调试日志 */
  debug?: boolean
}

export function useSmartScroll(options: SmartScrollOptions = {}) {
  const {
    bottomThreshold = 50,
    scrollDuration = 300,
    scrollBehavior = 'smooth',
    userScrollDebounce = 150,
    debug = false
  } = options

  // 状态管理
  const isAutoScrollEnabled = ref(true)
  const isUserScrolling = ref(false)
  const lastScrollTop = ref(0)
  const scrollContainer = ref<HTMLElement | null>(null)

  // 防抖定时器
  let userScrollTimer: NodeJS.Timeout | null = null
  let autoScrollTimer: NodeJS.Timeout | null = null

  // 调试日志
  const log = (...args: any[]) => {
    if (debug) {
      console.log('[SmartScroll]', ...args)
    }
  }

  /**
   * 检查是否接近底部
   */
  const isNearBottom = (): boolean => {
    if (!scrollContainer.value) return false

    const container = scrollContainer.value
    const scrollTop = container.scrollTop
    const scrollHeight = container.scrollHeight
    const clientHeight = container.clientHeight
    
    const distanceFromBottom = scrollHeight - scrollTop - clientHeight
    const isNear = distanceFromBottom <= bottomThreshold

    log('距离底部:', distanceFromBottom, '是否接近:', isNear)
    return isNear
  }

  /**
   * 平滑滚动到底部
   */
  const scrollToBottom = (force = false) => {
    if (!scrollContainer.value) return
    if (!isAutoScrollEnabled.value && !force) return

    const container = scrollContainer.value
    
    log('滚动到底部, 强制:', force)
    
    container.scrollTo({
      top: container.scrollHeight,
      behavior: scrollBehavior
    })
  }

  /**
   * 检测用户手动滚动
   */
  const handleUserScroll = () => {
    if (!scrollContainer.value) return

    const currentScrollTop = scrollContainer.value.scrollTop
    
    // 检测是否为用户主动滚动（非程序触发）
    if (Math.abs(currentScrollTop - lastScrollTop.value) > 5) {
      isUserScrolling.value = true
      
      // 如果用户向上滚动，禁用自动滚动
      if (currentScrollTop < lastScrollTop.value) {
        isAutoScrollEnabled.value = false
        log('用户向上滚动，禁用自动滚动')
      }
      
      // 清除之前的定时器
      if (userScrollTimer) {
        clearTimeout(userScrollTimer)
      }
      
      // 防抖：一段时间后检查是否接近底部
      userScrollTimer = setTimeout(() => {
        isUserScrolling.value = false
        
        // 如果用户滚动到底部附近，恢复自动滚动
        if (isNearBottom()) {
          isAutoScrollEnabled.value = true
          log('用户滚动到底部，恢复自动滚动')
        }
      }, userScrollDebounce)
    }
    
    lastScrollTop.value = currentScrollTop
  }

  /**
   * 智能滚动：根据内容变化自动滚动
   */
  const smartScroll = () => {
    if (!scrollContainer.value) return
    
    // 如果用户正在滚动，延迟执行
    if (isUserScrolling.value) {
      log('用户正在滚动，延迟自动滚动')
      if (autoScrollTimer) {
        clearTimeout(autoScrollTimer)
      }
      autoScrollTimer = setTimeout(smartScroll, 100)
      return
    }

    // 如果自动滚动被禁用，检查是否接近底部
    if (!isAutoScrollEnabled.value) {
      if (isNearBottom()) {
        isAutoScrollEnabled.value = true
        log('检测到接近底部，恢复自动滚动')
      } else {
        return
      }
    }

    // 执行滚动
    scrollToBottom()
  }

  /**
   * 处理触摸事件（移动端）
   */
  const handleTouchStart = () => {
    log('触摸开始')
    isUserScrolling.value = true
  }

  const handleTouchEnd = () => {
    log('触摸结束')
    setTimeout(() => {
      isUserScrolling.value = false
      if (isNearBottom()) {
        isAutoScrollEnabled.value = true
        log('触摸结束后检测到底部，恢复自动滚动')
      }
    }, userScrollDebounce)
  }

  /**
   * 处理鼠标滚轮事件
   */
  const handleWheel = (event: WheelEvent) => {
    if (event.deltaY !== 0) {
      log('鼠标滚轮滚动')
      handleUserScroll()
    }
  }

  /**
   * 初始化滚动容器
   */
  const initScrollContainer = (element: HTMLElement) => {
    if (scrollContainer.value) {
      removeEventListeners()
    }

    scrollContainer.value = element
    lastScrollTop.value = element.scrollTop

    // 添加事件监听器
    element.addEventListener('scroll', handleUserScroll, { passive: true })
    element.addEventListener('wheel', handleWheel, { passive: true })
    element.addEventListener('touchstart', handleTouchStart, { passive: true })
    element.addEventListener('touchend', handleTouchEnd, { passive: true })

    log('初始化滚动容器')
  }

  /**
   * 移除事件监听器
   */
  const removeEventListeners = () => {
    if (!scrollContainer.value) return

    const element = scrollContainer.value
    element.removeEventListener('scroll', handleUserScroll)
    element.removeEventListener('wheel', handleWheel)
    element.removeEventListener('touchstart', handleTouchStart)
    element.removeEventListener('touchend', handleTouchEnd)

    log('移除事件监听器')
  }

  /**
   * 强制启用自动滚动
   */
  const enableAutoScroll = () => {
    isAutoScrollEnabled.value = true
    isUserScrolling.value = false
    log('强制启用自动滚动')
  }

  /**
   * 禁用自动滚动
   */
  const disableAutoScroll = () => {
    isAutoScrollEnabled.value = false
    log('禁用自动滚动')
  }

  /**
   * 清理资源
   */
  const cleanup = () => {
    removeEventListeners()
    
    if (userScrollTimer) {
      clearTimeout(userScrollTimer)
      userScrollTimer = null
    }
    
    if (autoScrollTimer) {
      clearTimeout(autoScrollTimer)
      autoScrollTimer = null
    }
    
    log('清理资源')
  }

  // 组件卸载时清理
  onUnmounted(() => {
    cleanup()
  })

  return {
    // 状态
    isAutoScrollEnabled: readonly(isAutoScrollEnabled),
    isUserScrolling: readonly(isUserScrolling),
    
    // 方法
    initScrollContainer,
    smartScroll,
    scrollToBottom,
    enableAutoScroll,
    disableAutoScroll,
    isNearBottom,
    cleanup,
    
    // 内部状态（用于调试）
    scrollContainer: readonly(scrollContainer)
  }
}
