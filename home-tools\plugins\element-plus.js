import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import { defineNuxtPlugin } from '#app'
import { ID_INJECTION_KEY, ZINDEX_INJECTION_KEY } from 'element-plus'

export default defineNuxtPlugin((nuxtApp) => {
  nuxtApp.vueApp.provide(ID_INJECTION_KEY, {
    prefix: 1000,
    current: 0
  })

  // 提供zIndex上下文
  nuxtApp.vueApp.provide(ZINDEX_INJECTION_KEY, {
    current: 0,
    initial: 2000
  })
  
  nuxtApp.vueApp.use(ElementPlus, {
    // SSR相关配置
    ssr: true,
    // 禁用自动注入样式以避免hydration不匹配
    autoInsertSpace: false,
    // 设置弹出层容器为body，避免SSR hydration不匹配
    popperContainer: 'body',
    // 设置组件的基础z-index
    zIndex: 3000
  })
})