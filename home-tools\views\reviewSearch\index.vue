<template>
  <div class="flex h-full overflow-auto">
    <!-- 内容 -->
    <div class="w-[58%]">
      <!-- 查询 -->
      <div class="flex items-center bg-[#f7f8fa] p-2 rounded-md h-[52px] mb-2">
        <el-input
          class="h-full !text-[24px]"
          v-model="queryTitle"
          :placeholder="placeholder"
          clearable
        />
        <el-button type="primary" @click="getResult(1)">查 询</el-button>
      </div>
      <!-- 工具栏 -->
      <div class="flex items-center my-8">
        <!-- 翻译开关 -->
        <div class="flex items-center mr-4">
          <span class="mr-2" :class="translate ? 'text-[#409eff]' : ''"
            >翻译</span
          >
          <el-switch v-model="translate" />
        </div>
        <!-- 影响因子 -->
        <div class="flex items-center mr-4">
          <span class="mr-2">影响因子：</span>
          <el-checkbox-group v-model="checkList">
            <el-checkbox label="3"> {{ "<3分" }} </el-checkbox>
            <el-checkbox label="5">3-10分</el-checkbox>
            <el-checkbox label="10">{{ ">10分" }}</el-checkbox>
            <el-checkbox label="cns">{{ "CNS" }}</el-checkbox>
          </el-checkbox-group>
        </div>
      </div>
      <!-- 查询结果 -->
      <div>
        <div
          class="flex mb-8"
          v-for="(item, index) in result.content"
          :key="index"
        >
          <div class="mr-2">{{ index + 1 }}.</div>
          <div>
            <div
              class="text-[18px] text-[#5e5e5e] cursor-pointer html-value"
              v-html="item.text"
              @click="handleClick(index + 1, item.text)"
            ></div>
            <!-- 翻译 -->
            <div
              class="text-[16px] text-[#0a76f5] mt-4"
              v-html="item.translateText"
              v-if="translate"
            ></div>
          </div>
        </div>
      </div>
      <!-- 分页 -->
      <Pagination
        v-if="result && result.eleTotal"
        class="pb-10"
        :total="result.eleTotal"
        v-model:page="paging.pageNo"
        v-model:limit="paging.pageSize"
        @pagination="getResult"
      />
    </div>
    <!-- 编辑器 -->
    <div class="flex-1 ml-4 box-right">
      <Editor
        class="h-[380px] fixed z-99"
        :style="{width: editorWidth + 'px'}"
        v-model="htmlvalue"
        @clear="onClear"
        :key="editorKey"
      />
    </div>
  </div>
</template>

<script setup>
import { medsciAi } from "@/api/index";
import { shuffleArray } from "@/utils/index"
const queryTitle = ref("literature retrieval");
const htmlvalue = ref("");
const editorKey = ref(1);
const translate = ref(true);
const checkList = ref([]);
const result = ref({});
const placeholder = ref("请输入关键词或短句（中/英）");
const paging = reactive({
  pageNo: 1,
  pageSize: 20,
});
const editorWidth = ref(344)
// 获取数据
const getResult = (type) => {
  if (!queryTitle.value) return ElMessage.warning("请输入关键词或短句");
  if (type == 1) paging.pageNo = 1;
  let mulSearchConditions = checkList.value.map((item) => {
    if (item == "cns") {
      return {
        field: "cns",
        opt: 2,
        vals: [item],
        val: "",
        synonymsWordVos: [],
      };
    } else {
      return {
        field: "effect",
        opt: 2,
        vals: [item],
        val: "",
        synonymsWordVos: [],
      };
    }
  });
  medsciAi("review", {
    key: queryTitle.value,
    page: paging.pageNo - 1,
    size: paging.pageSize,
    allMySentence: 0,
    allMyGroupSentence: 0,
    synonymsHistory: 0,
    mulSearchConditions: mulSearchConditions,
  }).then((res) => {
    if (!res || !res.data) return
    result.value = res.data
    result.value.content = shuffleArray(result.value.content)
  });
};
// 点击粘贴
const handleClick = (index, text) => {
  let str = `${index}.${text}`.replace(/\n/g, "");
  htmlvalue.value += `<p>${str}</p>`;
};
// 编辑器清空时
const onClear = () => {
  editorKey.value++;
};

onMounted(() => {
  getResult();
  editorWidth.value = document.querySelector(".box-right").offsetWidth
  window.onresize = () => {
    editorWidth.value = document.querySelector(".box-right").offsetWidth
  }
});
</script>

<style lang="scss" scoped>
:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color))
    inset;
  background: #f7f8fa;
  cursor: default;
  .el-input__inner {
    cursor: default !important;
    color: #000;
    font-weight: 400;
  }
}
.html-value:hover {
  color: rgb(114, 205, 254);
  :deep(em) {
    color: rgb(114, 205, 254) !important;
  }
}
:deep(.el-checkbox) {
  margin-right: 10px;
}
</style>
