<template>
  <div class="page-wrapper">
    <div class="main-container">
      <div class="article-container">
        <div v-if="loading" class="loading-container">加载中...</div>
        <div v-else-if="error" class="error-container">无法加载文章</div>
        <template v-else>
          <!-- 文章头部 -->
          <div class="article-header">
            <h1 class="article-title">{{ article.title }}</h1>
            <div class="meta-info">
              <span v-if="article.publishedTimeString" class="publish-time">{{ article.publishedTimeString }}</span>
              <span v-if="article.createdName" class="author">{{ article.createdName }}</span>
              <span v-if="article.ipAttribution" class="location">发表于{{ article.ipAttribution }}</span>
            </div>
          </div>

          <!-- 文章封面图 -->
          <div v-if="article.cover" class="cover-image">
            <img :src="article.cover" :alt="article.title" />
          </div>

          <!-- 文章分类标签 -->
          <div v-if="seoKeywordsList && seoKeywordsList.length > 0" class="categories">
            <span 
              v-for="(keyword, index) in seoKeywordsList" 
              :key="index" 
              class="category"
            >
              {{ keyword }}
            </span>
          </div>

          <!-- 文章摘要 -->
          <div v-if="article.summary" class="article-summary">
            {{ article.summary }}
          </div>

          <!-- 显示文章内容 -->
          <div v-if="article.content" class="article-content" v-html="decodedContent"></div>
          <pre v-else>{{ JSON.stringify(article, null, 2) }}</pre>
        </template>
      </div>
      
      <!-- 右侧边栏 -->
      <div class="sidebar">
        <!-- 工具跳转区域 -->
        <div v-if="seoData" class="sidebar-section tool-section">
          <div class="section-title">工具使用</div>
          <div class="tool-card">
            <div class="tool-icon">
              <img :src="seoData.app_icon || 'https://www.medsci.cn/images/logo.png'" alt="工具图标" />
            </div>
            <div class="tool-info">
              <div class="tool-name">{{ seoData.app_name || '工具名称' }}</div>
              <button @click="handleToolClick" class="use-button">使用</button>
            </div>
          </div>
        </div>
        
        <!-- 最近文章 -->
        <div class="sidebar-section">
          <div class="section-title">最新文章</div>
          <div v-if="loadingQuestions" class="loading-side">加载中...</div>
          <div v-else>
            <div v-for="(item, index) in recentArticles.slice(0, 3)" :key="index" class="article-item" @click="redirectToArticle(item.uuid)">
              <div class="question-title">{{ item.title }}</div>
              <div class="question-meta">{{ formatDate(item.publishedTime || item.createdTime) }}</div>
            </div>
            <div v-if="!recentArticles || recentArticles.length === 0" class="empty-data">
              暂无数据
            </div>
          </div>
        </div>
        
        <!-- 相关文章 -->
        <div class="sidebar-section">
          <div class="section-title">相关文章</div>
          <div v-if="loadingRelated" class="loading-side">加载中...</div>
          <div v-else>
            <div v-for="(item, index) in relatedArticles.slice(0, 3)" :key="index" class="article-item" @click="redirectToArticle(item.uuid)">
              <div class="related-article-title">{{ item.title }}</div>
              <div class="related-article-meta">{{ formatDate(item.publishedTime || item.createdTime) }}</div>
            </div>
            <div v-if="!relatedArticles || relatedArticles.length === 0" class="empty-data">
              暂无数据
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部版权信息 -->
    <div class="article-footer">
      <div class="footer-bottom">
        <div class="copyright">
          ©Copyright 2012-至今 梅斯（MedSci）
        </div>
        <div class="license-info">
          增值电信业务经营许可证 | 备案号 沪ICP备14018916号-1 | 互联网药品信息服务资格证书((沪)-非经营性-2020-0033) | 出版物经营许可证
        </div>
        <div class="license-info">
          上海工商 | 上海网警网络110 | 网络社会征信网 | 违法和不良信息举报中心 | 信息举报中心 |违法举报：021-54485309 | 沪公网安备 31010402000380
        </div>
        <div class="footer-info">
          本站旨在介绍医药健康研究进展和信息，不作为诊疗方案推荐。如需获得诊断或治疗方面指导，请前往正规医院就诊。
        </div>
        <div class="footer-info">
          用户应遵守著作权法，尊重著作权人合法权益，不违法上传、存储并分享他人作品。投诉、举报、维权邮箱：<EMAIL>，或在此留言
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { getArticleWithSEO } from '@/api/base';
import { useRoute } from 'vue-router';
import { useAsyncData, useHead } from '#imports';

const route = useRoute();
const article = ref({});
const seoData = ref(null);
const loading = ref(true);
const error = ref(false);
const dataLoaded = ref(false); // 添加标记确保数据只加载一次

// 侧边栏数据
const recentArticles = ref([]);
const relatedArticles = ref([]);
const loadingQuestions = ref(true);
const loadingRelated = ref(true);

// 处理文章内容中的HTML实体
const decodedContent = computed(() => {
  if (!article.value.content) {
    console.log('文章内容为空');
    return '';
  }
  
  console.log('处理文章内容:', article.value.content.substring(0, 50) + '...');
  const content = article.value.content;
  
  // 在服务器端无法使用document，使用正则替换HTML实体
  const decoded = content
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&amp;/g, '&')
    .replace(/&ldquo;/g, '"')
    .replace(/&rdquo;/g, '"')
    .replace(/&hellip;/g, '...')
    .replace(/&nbsp;/g, ' ');
  
  console.log('解码后的内容:', decoded.substring(0, 50) + '...');
  return decoded;
});

// 解析JSON字符串为对象
const parseSeoData = (jsonStr) => {
  if (!jsonStr) return null;
  console.log('解析SEO数据:', jsonStr);
  try {
    return typeof jsonStr === 'string' ? JSON.parse(jsonStr) : jsonStr;
  } catch (e) {
    console.error('解析SEO数据失败:', e);
    return null;
  }
};

// 从seo数据中获取SEO标题
const seoTitle = computed(() => {
  if (!seoData.value?.seo_title) {
    console.log('未找到SEO标题, 使用文章标题:', article.value.title);
    return article.value.title;
  }
  
  const titleData = parseSeoData(seoData.value.seo_title);
  console.log('解析后的SEO标题数据:', titleData);
  return titleData?.primary || article.value.title;
});

// 从seo数据中获取SEO描述
const seoDescription = computed(() => {
  if (!seoData.value?.seo_description) {
    console.log('未找到SEO描述, 使用文章摘要:', article.value.summary);
    return article.value.summary;
  }
  
  const descData = parseSeoData(seoData.value.seo_description);
  console.log('解析后的SEO描述数据:', descData);
  return descData?.core || article.value.summary;
});

// 从seo_description中获取关键词列表
const seoKeywordsList = computed(() => {
  if (!seoData.value?.seo_description) return [];
  
  try {
    const descData = parseSeoData(seoData.value.seo_description);
    console.log('解析后的SEO描述数据(keywords):', descData?.keywords);
    const keywords = Array.isArray(descData?.keywords) ? descData.keywords : [];
    // 过滤掉"梅斯医学"关键词
    return keywords.filter(keyword => keyword !== '梅斯医学');
  } catch (e) {
    console.error('解析SEO关键词失败:', e);
    return [];
  }
});

// SEO关键词
const seoKeywords = computed(() => {
  if (seoKeywordsList.value.length > 0) {
    return seoKeywordsList.value.join(',');
  }
  return article.value.articleKeyword;
});

// 使用Nuxt的asyncData功能在服务器端预获取数据
const { data } = await useAsyncData(
  'articleData',
  async (nuxtApp) => {
    try {
      const uuid = route.params.id;
      const event = useRequestEvent();
      console.log('请求ID:', uuid);
      
      // 直接使用axios获取数据
      let responseData;
      try {
        const axios = (await import('axios')).default;
        const baseUrl = typeof window === 'undefined' ? 'http://localhost:48081' : '';
        const url = `${baseUrl}/ai-base/index/snapshot/getArticleWithSEO`;
        
        console.log('发送请求到:', url);
        const response = await axios.get(url, { 
          params: { id: uuid }
        });
        
        console.log('请求状态:', response.status);
        
        if (response.status === 200 && response.data && response.data.code === 0) {
          responseData = response.data.data;
          console.log('获取到响应数据');
        } else {
          throw new Error('响应格式错误');
        }
      } catch (axiosErr) {
        console.error('直接请求失败，尝试API函数:', axiosErr.message);
        responseData = await getArticleWithSEO(uuid, event);
      }
      
      console.log('获取到的响应:', responseData);

      // 设置侧边栏数据
      if (responseData && responseData.recentArticles) {
        console.log('设置最新文章数据，数量:', responseData.recentArticles.length);
        recentArticles.value = responseData.recentArticles;
      } else {
        console.error('响应中缺少recentArticles数据');
      }
      
      if (responseData && responseData.relatedArticles) {
        console.log('设置相关文章数据，数量:', responseData.relatedArticles.length);
        relatedArticles.value = responseData.relatedArticles;
      } else {
        console.error('响应中缺少relatedArticles数据');
      }
      
      loadingQuestions.value = false;
      loadingRelated.value = false;

      // 检查responseData是否包含seo和article
      if (responseData && responseData.seo && responseData.article) {
        console.log('成功提取数据结构');
        dataLoaded.value = true;
        return { 
          seo: responseData.seo, 
          articleData: responseData.article,
          recentArticles: responseData.recentArticles || [],
          relatedArticles: responseData.relatedArticles || [],
          error: false 
        };
      }
      
      console.error('响应数据不符合预期结构');
      return { 
        seo: null, 
        articleData: null, 
        recentArticles: [], 
        relatedArticles: [], 
        error: true 
      };
    } catch (err) {
      console.error('获取数据失败:', err.message);
      loadingQuestions.value = false;
      loadingRelated.value = false;
      return { 
        seo: null, 
        articleData: null, 
        recentArticles: [], 
        relatedArticles: [], 
        error: true 
      };
    }
  }
);

// 设置引用数据 - 确保正确解构
if (data.value) {
  console.log('从useAsyncData获取的数据:', data.value);
  seoData.value = data.value.seo;
  article.value = data.value.articleData || {};
  
  // 设置侧边栏数据
  if (data.value.recentArticles && data.value.recentArticles.length > 0) {
    recentArticles.value = data.value.recentArticles;
    console.log('设置最新文章:', recentArticles.value.length, '条');
  }
  
  if (data.value.relatedArticles && data.value.relatedArticles.length > 0) {
    relatedArticles.value = data.value.relatedArticles;
    console.log('设置相关文章:', relatedArticles.value.length, '条');
  }
  
  error.value = data.value.error;
  loading.value = false;
  loadingQuestions.value = false;
  loadingRelated.value = false;
  dataLoaded.value = true;
  console.log('设置到组件的数据:', 
    'seo:', seoData.value ? '有值' : 'null', 
    'article:', Object.keys(article.value).length ? '有值' : '空对象',
    'recentArticles:', recentArticles.value.length,
    'relatedArticles:', relatedArticles.value.length
  );
}

// 设置页面头部信息
useHead({
  title: computed(() => seoTitle.value ? `${seoTitle.value} - 梅斯医学` : '文章 - 梅斯医学'),
  meta: [
    {
      name: 'description',
      content: computed(() => seoDescription.value || '')
    },
    {
      name: 'keywords',
      content: computed(() => seoKeywords.value || '')
    },
    // Open Graph 元标签
    {
      property: 'og:title',
      content: computed(() => seoTitle.value || '')
    },
    {
      property: 'og:description',
      content: computed(() => seoDescription.value || '')
    },
    {
      property: 'og:image',
      content: computed(() => article.value.cover || '')
    },
    {
      property: 'og:type',
      content: 'article'
    }
  ]
});

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 跳转到文章页面
const redirectToArticle = (uuid) => {
  if (!uuid) return;
  
  const currentUrl = window.location.origin;
  window.open(`${currentUrl}/article/${uuid}`, "_blank");
};

// 处理工具点击
const handleToolClick = () => {
  if (!seoData.value) return;
  
  const item = seoData.value;
  const currentUrl = window.location.origin;
  
  if (item.app_type === "工具") {
    window.open(`${currentUrl}/tool/${item.app_name_en || item.app_name}`, "_blank");
  } else if (item.app_type === "问答") {
    window.open(`${currentUrl}/chat/${item.app_name_en || item.app_name}`, "_blank");
  } else if (item.app_type === "写作") {
    localStorage.setItem(
      "appWrite" + '-' + item.app_uuid,
      JSON.stringify({
        appUuid: item.app_uuid,
        directoryMd: item.directory_md || '',
      })
    );
    window.open(`${window.location.origin}/write/${item.app_name_en || item.app_name}`);
  }
};
</script>

<style scoped>
.page-wrapper {
  display: flex;
  flex-direction: column;
  min-height: 100vh; /* 确保最小高度占满视窗 */
}

.main-container {
  display: flex;
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.article-container {
  flex: 3;
  margin-right: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  color: #333;
  line-height: 1.6;
}

.sidebar {
  flex: 1;
  min-width: 280px;
  max-width: 320px;
}

.sidebar-section {
  background-color: #f8f8f8;
  border-radius: 8px;
  margin-bottom: 20px;
  padding: 15px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #eee;
}

/* 工具区域样式 */
.tool-section {
  background-color: #f0f7ff;
}

.tool-card {
  display: flex;
  align-items: center;
  padding: 10px;
  background: white;
  border-radius: 6px;
}

.tool-icon {
  width: 50px;
  height: 50px;
  margin-right: 12px;
}

.tool-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 6px;
}

.tool-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.tool-name {
  font-weight: bold;
  margin-bottom: 8px;
}

.use-button {
  background-color: #1890ff;
  color: white;
  border: none;
  padding: 5px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  align-self: flex-start;
}

.use-button:hover {
  background-color: #40a9ff;
}

/* 问答区域样式 */
.question-item, .article-item {
  padding: 10px;
  background: white;
  border-radius: 6px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.2s;
}

.question-item:hover, .article-item:hover {
  background-color: #f0f7ff;
  transform: translateY(-2px);
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.question-title, .related-article-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 5px;
  color: #333;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.question-meta, .related-article-meta {
  font-size: 12px;
  color: #999;
}

.loading-side {
  text-align: center;
  color: #999;
  padding: 15px 0;
}

.empty-data {
  text-align: center;
  color: #999;
  padding: 15px 0;
  font-size: 14px;
}

.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  font-size: 18px;
  color: #666;
}

.article-header {
  margin-bottom: 20px;
}

.article-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #333;
}

.meta-info {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.publish-time, .author, .location {
  margin-right: 15px;
}

.categories {
  display: flex;
  flex-wrap: wrap;
  margin-top: 10px;
}

.category {
  background-color: #f0f0f0;
  padding: 4px 8px;
  margin-right: 8px;
  margin-bottom: 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
}

.cover-image {
  margin-bottom: 20px;
}

.cover-image img {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
}

.article-summary {
  font-size: 16px;
  background-color: #f5f5f5;
  padding: 15px;
  border-left: 4px solid #eee;
  margin-bottom: 20px;
  color: #555;
}

.article-content {
  font-size: 16px;
  margin-bottom: 30px;
}

.article-content :deep(a) {
  color: #3498db;
  text-decoration: none;
}

.article-content :deep(a:hover) {
  text-decoration: underline;
}

.article-content :deep(p) {
  margin-bottom: 16px;
}

.article-content :deep(img) {
  max-width: 100%;
  height: auto;
  margin: 10px 0;
}

.tags-container {
  margin-top: 30px;
  margin-bottom: 30px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.tags-container h3 {
  font-size: 18px;
  margin-bottom: 15px;
  color: #333;
}

.tag-item {
  margin-bottom: 10px;
}

.tag-name {
  font-size: 16px;
  font-weight: bold;
  color: #2980b9;
}

.tag-info {
  margin-top: 5px;
  font-size: 12px;
  color: #777;
}

.article-footer {
  margin-top: 30px;
  padding: 20px 0 0;
  background-color: #333;
  color: #ccc;
  width: 100%;
}

.footer-bottom {
  padding: 20px;
  text-align: center;
}

.copyright {
  margin-bottom: 10px;
  font-weight: 600;
  color: #ddd;
}

.license-info,
.footer-info {
  margin-bottom: 10px;
  line-height: 1.5;
  font-size: 12px;
  color: #aaa;
}

/* 关键词标签样式 */
.keywords-tags {
  display: none;
}

.keyword-tag {
  display: none;
}
</style> 