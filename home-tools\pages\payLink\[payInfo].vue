

<template>
  <div>
    <button @click="goIndex">返回首页</button>
  </div>
</template>

<script setup>
import { useRoute } from "vue-router";
import { createAliSub, getSubOrder } from "@/api/base";
import { onMounted, ref } from "vue";
import { ElMessage } from "element-plus";
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const route = useRoute();
const time = ref()
const payInfo = ref("")
// piId="+payInfo.piId+"&socialUserId="+payInfo.socialUserId+"&openid="+payInfo.openid+"&sessionId="+payInfo.sessionId
onMounted(async () => {
   payInfo.value = JSON.parse(decodeURIComponent(route.params.payInfo));
  await getStatus()
  const userAgent = navigator.userAgent;
  if (userAgent != null) {
    if (userAgent.includes("MicroMessenger")) {
        ElMessage.warning(t("tool.pleasescanwithalipay"))
    } else if (userAgent.includes("AlipayClient")) {
      const res = await createAliSub(payInfo.value);
      location.replace(res);
    }
  }
});
// 返回首页
const goIndex=() =>{
  location.replace(location.origin) 
}
// 查询支付状态
const getStatus =  ()=>{
  time.value = setInterval(async() => {
    const res = await getSubOrder(payInfo.value?.piId)
    if (res.payStatus === 'PAID') {
        location.replace(location.origin) 
        clearInterval(time.value)
    }
  },2000)
}
</script>
<style lang="scss" scoped>
div {
  display: flex;
  justify-content: center;
  margin-top: 10vh;
  button {
    height: 40px;
    line-height: 38px;
    opacity: 1;
    border: 2px solid #2680eb;
    border-radius: 10px;
    background: #fff;
    font-size: 16px;
    font-family: PingFang SC, PingFang SC-Regular;
    font-weight: 400;
    color: #2680eb;
    cursor: pointer;
  }
}
</style>
